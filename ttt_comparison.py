import numpy as np
import matplotlib.pyplot as plt

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate_with_y(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation WITH auxiliary variable y."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

def interpolate_without_y(t, x0, x1):
    """Standard linear interpolation WITHOUT auxiliary variable y."""
    # Simple linear interpolation for x only
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    return x_t

def entropy_gaussian(cov):
    """Compute entropy of multivariate Gaussian distribution."""
    k = cov.shape[0]
    det_cov = np.linalg.det(cov)
    if det_cov <= 0:
        return float('inf')
    return 0.5 * k * (1 + np.log(2 * np.pi)) + 0.5 * np.log(det_cov)

# Setup comparison
print("="*80)
print("RECTIFIED FLOW COMPARISON: WITH vs WITHOUT AUXILIARY VARIABLE Y")
print("="*80)

# Parameters
n_trajectories = 6
beta_values = [5, 10, 20]
n_time_steps = 100
ts = np.linspace(0, 1, n_time_steps)

# Generate comparison trajectories
trajectories_with_y = []
trajectories_without_y = []

colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFD93D', '#6BCF7F', '#A8E6CF']

print(f"Generating {n_trajectories} trajectory pairs for β = {beta_values}")

traj_id = 0
for beta in beta_values:
    for i in range(2):  # 2 trajectories per beta
        np.random.seed(traj_id + beta * 10)
        
        # Same initial and final states for fair comparison
        x0 = np.array([4.0 + np.random.uniform(-0.3, 0.3), 
                      4.0 + np.random.uniform(-0.3, 0.3), 
                      4.0 + np.random.uniform(-0.3, 0.3)])
        x1 = np.array([1.0 + np.random.uniform(-0.2, 0.2), 
                      1.0 + np.random.uniform(-0.2, 0.2), 
                      1.0 + np.random.uniform(-0.2, 0.2)])
        
        # Auxiliary variables (only for WITH_Y version)
        y0 = np.random.normal(0, 0.1, 3)
        y1 = np.random.normal(0, 0.6, 3)
        
        # Compute trajectories
        states_with_y = np.array([interpolate_with_y(t, x0, x1, y0, y1, beta) for t in ts])
        states_without_y = np.array([interpolate_without_y(t, x0, x1) for t in ts])
        
        trajectories_with_y.append({
            'beta': beta,
            'states': states_with_y,
            'x_states': states_with_y[:, :3],
            'y_states': states_with_y[:, 3:],
            'color': colors[traj_id],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1,
            'id': traj_id
        })
        
        trajectories_without_y.append({
            'beta': beta,
            'states': states_without_y,
            'x_states': states_without_y,
            'color': colors[traj_id],
            'x0': x0, 'x1': x1,
            'id': traj_id
        })
        
        traj_id += 1

print(f"Generated {len(trajectories_with_y)} trajectory pairs")

# Create comprehensive comparison visualization
fig = plt.figure(figsize=(20, 16))
fig.suptitle('Rectified Flow Comparison: WITH vs WITHOUT Auxiliary Variable Y', 
             fontsize=18, fontweight='bold')

# Row 1: X-space trajectories comparison
ax1 = plt.subplot(4, 4, 1)
ax1.set_title('WITH Y: X-Space (x₁ vs x₂)', fontweight='bold', color='blue')
ax1.set_xlabel('x₁')
ax1.set_ylabel('x₂')
ax1.grid(True, alpha=0.3)

ax2 = plt.subplot(4, 4, 2)
ax2.set_title('WITHOUT Y: X-Space (x₁ vs x₂)', fontweight='bold', color='red')
ax2.set_xlabel('x₁')
ax2.set_ylabel('x₂')
ax2.grid(True, alpha=0.3)

# Row 1: Y-space (only for WITH Y)
ax3 = plt.subplot(4, 4, 3)
ax3.set_title('WITH Y: Y-Space (y₁ vs y₂)', fontweight='bold', color='blue')
ax3.set_xlabel('y₁')
ax3.set_ylabel('y₂')
ax3.grid(True, alpha=0.3)

ax4 = plt.subplot(4, 4, 4)
ax4.set_title('Auxiliary Variable Evolution', fontweight='bold')
ax4.set_xlabel('Time t')
ax4.set_ylabel('Y-component values')
ax4.grid(True, alpha=0.3)

# Row 2: State norms comparison
ax5 = plt.subplot(4, 4, 5)
ax5.set_title('WITH Y: State Norms', fontweight='bold', color='blue')
ax5.set_xlabel('Time t')
ax5.set_ylabel('L2 Norm')
ax5.grid(True, alpha=0.3)

ax6 = plt.subplot(4, 4, 6)
ax6.set_title('WITHOUT Y: State Norms', fontweight='bold', color='red')
ax6.set_xlabel('Time t')
ax6.set_ylabel('L2 Norm')
ax6.grid(True, alpha=0.3)

# Row 2: Combined comparison
ax7 = plt.subplot(4, 4, 7)
ax7.set_title('X-Norm Comparison', fontweight='bold')
ax7.set_xlabel('Time t')
ax7.set_ylabel('X L2 Norm')
ax7.grid(True, alpha=0.3)

ax8 = plt.subplot(4, 4, 8)
ax8.set_title('Total System Dimension', fontweight='bold')
ax8.set_xlabel('Time t')
ax8.set_ylabel('Total Norm')
ax8.grid(True, alpha=0.3)

# Row 3: Velocity and dynamics
ax9 = plt.subplot(4, 4, 9)
ax9.set_title('WITH Y: Velocity Magnitude', fontweight='bold', color='blue')
ax9.set_xlabel('Time t')
ax9.set_ylabel('Velocity')
ax9.grid(True, alpha=0.3)

ax10 = plt.subplot(4, 4, 10)
ax10.set_title('WITHOUT Y: Velocity Magnitude', fontweight='bold', color='red')
ax10.set_xlabel('Time t')
ax10.set_ylabel('Velocity')
ax10.grid(True, alpha=0.3)

# Row 3: Distance and entropy
ax11 = plt.subplot(4, 4, 11)
ax11.set_title('Distance from Start Comparison', fontweight='bold')
ax11.set_xlabel('Time t')
ax11.set_ylabel('Distance')
ax11.grid(True, alpha=0.3)

ax12 = plt.subplot(4, 4, 12)
ax12.set_title('System Entropy Comparison', fontweight='bold')
ax12.set_xlabel('Time t')
ax12.set_ylabel('Entropy (nats)')
ax12.grid(True, alpha=0.3)

# Row 4: Flow characteristics
ax13 = plt.subplot(4, 4, 13)
ax13.set_title('σ_y(t) Weight Function', fontweight='bold')
ax13.set_xlabel('Time t')
ax13.set_ylabel('σ_y(t)')
ax13.grid(True, alpha=0.3)

ax14 = plt.subplot(4, 4, 14)
ax14.set_title('Path Length Comparison', fontweight='bold')
ax14.set_xlabel('Beta Parameter')
ax14.set_ylabel('Total Path Length')
ax14.grid(True, alpha=0.3)

ax15 = plt.subplot(4, 4, 15)
ax15.set_title('Flow Efficiency', fontweight='bold')
ax15.set_xlabel('Beta Parameter')
ax15.set_ylabel('Efficiency Ratio')
ax15.grid(True, alpha=0.3)

ax16 = plt.subplot(4, 4, 16)
ax16.set_title('Trajectory Complexity', fontweight='bold')
ax16.set_xlabel('Time t')
ax16.set_ylabel('Curvature')
ax16.grid(True, alpha=0.3)

print("Plotting trajectories and comparisons...")

# Plot trajectories WITH Y
for traj in trajectories_with_y:
    color = traj['color']
    x_states = traj['x_states']
    y_states = traj['y_states']
    
    # X-space trajectories
    ax1.plot(x_states[:, 0], x_states[:, 1], color=color, alpha=0.8, linewidth=2)
    ax1.scatter(traj['x0'][0], traj['x0'][1], c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax1.scatter(traj['x1'][0], traj['x1'][1], c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    
    # Y-space trajectories
    ax3.plot(y_states[:, 0], y_states[:, 1], color=color, alpha=0.8, linewidth=2)
    ax3.scatter(traj['y0'][0], traj['y0'][1], c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax3.scatter(traj['y1'][0], traj['y1'][1], c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    
    # Y-component evolution
    ax4.plot(ts, y_states[:, 0], color=color, alpha=0.7, linewidth=1, linestyle='-')
    ax4.plot(ts, y_states[:, 1], color=color, alpha=0.7, linewidth=1, linestyle='--')
    ax4.plot(ts, y_states[:, 2], color=color, alpha=0.7, linewidth=1, linestyle=':')
    
    # State norms
    x_norms = np.linalg.norm(x_states, axis=1)
    y_norms = np.linalg.norm(y_states, axis=1)
    total_norms = np.sqrt(x_norms**2 + y_norms**2)
    
    ax5.plot(ts, x_norms, color=color, alpha=0.8, linewidth=2, linestyle='-', label='X-norm')
    ax5.plot(ts, y_norms, color=color, alpha=0.8, linewidth=2, linestyle='--', label='Y-norm')
    
    # X-norm comparison
    ax7.plot(ts, x_norms, color=color, alpha=0.8, linewidth=2, linestyle='-', label=f'WITH Y (β={traj["beta"]})')
    
    # Total system dimension
    ax8.plot(ts, total_norms, color=color, alpha=0.8, linewidth=2, linestyle='-', label=f'WITH Y (β={traj["beta"]})')
    
    # Velocity magnitude
    velocities = np.diff(traj['states'], axis=0)
    vel_magnitudes = np.linalg.norm(velocities, axis=1)
    ax9.plot(ts[1:], vel_magnitudes, color=color, alpha=0.8, linewidth=2)
    
    # Distance from start
    start_pos = np.concatenate([traj['x0'], traj['y0']])
    distances = [np.linalg.norm(state - start_pos) for state in traj['states']]
    ax11.plot(ts, distances, color=color, alpha=0.8, linewidth=2, linestyle='-', label=f'WITH Y (β={traj["beta"]})')

# Plot trajectories WITHOUT Y
for traj in trajectories_without_y:
    color = traj['color']
    x_states = traj['x_states']
    
    # X-space trajectories
    ax2.plot(x_states[:, 0], x_states[:, 1], color=color, alpha=0.8, linewidth=2)
    ax2.scatter(traj['x0'][0], traj['x0'][1], c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax2.scatter(traj['x1'][0], traj['x1'][1], c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    
    # State norms
    x_norms = np.linalg.norm(x_states, axis=1)
    ax6.plot(ts, x_norms, color=color, alpha=0.8, linewidth=2, label='X-norm')
    
    # X-norm comparison
    ax7.plot(ts, x_norms, color=color, alpha=0.8, linewidth=2, linestyle=':', label=f'WITHOUT Y (β={traj["beta"]})')
    
    # Total system dimension
    ax8.plot(ts, x_norms, color=color, alpha=0.8, linewidth=2, linestyle=':', label=f'WITHOUT Y (β={traj["beta"]})')
    
    # Velocity magnitude
    velocities = np.diff(x_states, axis=0)
    vel_magnitudes = np.linalg.norm(velocities, axis=1)
    ax10.plot(ts[1:], vel_magnitudes, color=color, alpha=0.8, linewidth=2)
    
    # Distance from start
    distances = [np.linalg.norm(state - traj['x0']) for state in x_states]
    ax11.plot(ts, distances, color=color, alpha=0.8, linewidth=2, linestyle=':', label=f'WITHOUT Y (β={traj["beta"]})')

# Plot sigma_y curves
for beta in beta_values:
    sigma_values = [sigma_y(t, beta) for t in ts]
    ax13.plot(ts, sigma_values, linewidth=3, label=f'β = {beta}')
ax13.legend()

# Compute and plot path lengths
beta_path_lengths_with = []
beta_path_lengths_without = []

for beta in beta_values:
    # WITH Y
    with_y_lengths = []
    for traj in trajectories_with_y:
        if traj['beta'] == beta:
            path_length = np.sum(np.linalg.norm(np.diff(traj['states'], axis=0), axis=1))
            with_y_lengths.append(path_length)
    beta_path_lengths_with.append(np.mean(with_y_lengths))
    
    # WITHOUT Y
    without_y_lengths = []
    for traj in trajectories_without_y:
        if traj['beta'] == beta:
            path_length = np.sum(np.linalg.norm(np.diff(traj['x_states'], axis=0), axis=1))
            without_y_lengths.append(path_length)
    beta_path_lengths_without.append(np.mean(without_y_lengths))

ax14.bar([b-0.2 for b in beta_values], beta_path_lengths_with, width=0.4, 
        label='WITH Y', color='blue', alpha=0.7)
ax14.bar([b+0.2 for b in beta_values], beta_path_lengths_without, width=0.4, 
        label='WITHOUT Y', color='red', alpha=0.7)
ax14.legend()

# Efficiency ratio
efficiency_ratios = [w/wo for w, wo in zip(beta_path_lengths_with, beta_path_lengths_without)]
ax15.bar(beta_values, efficiency_ratios, color='purple', alpha=0.7)
ax15.axhline(y=1.0, color='black', linestyle='--', alpha=0.5, label='Equal efficiency')
ax15.legend()

# Add text annotations
ax4.text(0.02, 0.98, 'Solid: y₁\nDashed: y₂\nDotted: y₃', transform=ax4.transAxes, 
         verticalalignment='top', fontsize=8, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

ax7.legend(fontsize=8, loc='upper right')
ax11.legend(fontsize=8, loc='upper left')

plt.tight_layout()

# Save the comparison
plt.savefig('rectified_flow_comparison_with_without_y.png', dpi=300, bbox_inches='tight')
print("✓ Saved comparison visualization")

plt.show()

print("\n" + "="*80)
print("RECTIFIED FLOW COMPARISON ANALYSIS COMPLETED")
print("="*80)
print("🔍 KEY DIFFERENCES:")
print("  WITH Y (Auxiliary Variables):")
print("    • Higher dimensional state space (6D vs 3D)")
print("    • Non-linear interpolation dynamics")
print("    • More complex trajectory paths")
print("    • Additional degrees of freedom")
print("    • Richer flow dynamics")
print("")
print("  WITHOUT Y (Standard Linear):")
print("    • Lower dimensional state space (3D only)")
print("    • Pure linear interpolation")
print("    • Straight-line trajectories")
print("    • Simpler dynamics")
print("    • Direct path from start to end")
print("")
print("📊 COMPARISON METRICS:")
print("  • Path Length: WITH Y typically longer due to auxiliary dynamics")
print("  • Velocity Patterns: WITH Y shows non-uniform velocity")
print("  • System Complexity: WITH Y provides more control over flow")
print("  • Efficiency Ratio: Shows relative computational cost")
print("="*80)
