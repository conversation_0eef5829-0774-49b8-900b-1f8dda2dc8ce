2025-07-29 22:04:48,949 INFO: 
                ____                _       _____  ____
               / __ ) ____ _ _____ (_)_____/ ___/ / __ \
              / __  |/ __ `// ___// // ___/\__ \ / /_/ /
             / /_/ // /_/ /(__  )/ // /__ ___/ // _, _/
            /_____/ \__,_//____//_/ \___//____//_/ |_|
     ______                   __   __                 __      __
    / ____/____   ____   ____/ /  / /   __  __ _____ / /__   / /
   / / __ / __ \ / __ \ / __  /  / /   / / / // ___// //_/  / /
  / /_/ // /_/ // /_/ // /_/ /  / /___/ /_/ // /__ / /<    /_/
  \____/ \____/ \____/ \____/  /_____/\____/ \___//_/|_|  (_)
    
Version Information: 
	BasicSR: 1.4.2
	PyTorch: 2.4.1+cu121
	TorchVision: 0.19.1+cu121
2025-07-29 22:04:48,950 INFO: 
  name: PolarFree_InvertibleFlow_x4
  model_type: PolarFree_InvertibleFlow
  scale: 4
  num_gpu: 1
  manual_seed: 0
  datasets:[
    train:[
      name: PolarTrain
      type: PolarDataset
      dataroot_gt: datasets/polar_train/gt
      dataroot_lq: datasets/polar_train/lq
      filename_tmpl: {}
      io_backend:[
        type: disk
      ]
      gt_size: 256
      use_hflip: True
      use_rot: False
      use_shuffle: True
      num_worker_per_gpu: 6
      batch_size_per_gpu: 4
      dataset_enlarge_ratio: 1
      prefetch_mode: None
      phase: train
      scale: 4
    ]
    val:[
      name: PolarVal
      type: PolarDataset
      dataroot_gt: datasets/polar_val/gt
      dataroot_lq: datasets/polar_val/lq
      io_backend:[
        type: disk
      ]
      phase: val
      scale: 4
    ]
  ]
  network_g:[
    type: Transformer
    inp_channels: 3
    out_channels: 3
    dim: 48
    num_blocks: [3, 4, 4, 4]
    num_refinement_blocks: 4
    heads: [1, 2, 4, 8]
    ffn_expansion_factor: 2.66
    bias: False
    LayerNorm_type: WithBias
    dual_pixel_task: False
    embed_dim: 64
    group: 4
  ]
  network_le:[
    type: latent_encoder_gelu
    in_chans: 12
    embed_dim: 64
    block_num: 6
    group: 4
    stage: 1
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_inv_flow:[
    type: InvertibleFlowMatching
    in_channels: 64
    out_channels: 64
    model_channels: 128
    num_blocks: 4
    condition_dim: 64
    subnet_type: Resnet
    time_embed_dim: 512
    use_haar_downsampling: True
    sigma: 0.0
    clamp: 1.0
  ]
  network_flow_condition:[
    type: latent_encoder_gelu
    in_chans: 17
    embed_dim: 64
    block_num: 4
    group: 4
    stage: 2
    patch_expansion: 0.5
    channel_expansion: 2
  ]
  invertible_flow:[
    num_steps: 50
    solver: euler
    sigma: 0.0
    loss_weight: 1.0
    recon_loss_weight: 0.1
    use_conditioning: True
  ]
  path:[
    pretrain_network_g: experiments/pretrained_models/polarfree_transformer.pth
    pretrain_network_le: experiments/pretrained_models/polarfree_latent_encoder.pth
    strict_load_g: False
    strict_load_le: False
    resume_state: None
    experiments_root: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/PolarFree_InvertibleFlow_x4
    models: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/PolarFree_InvertibleFlow_x4/models
    training_states: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/PolarFree_InvertibleFlow_x4/training_states
    log: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/PolarFree_InvertibleFlow_x4
    visualization: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/PolarFree_InvertibleFlow_x4/visualization
  ]
  train:[
    ema_decay: 0.999
    optim_g:[
      type: Adam
      lr: 0.0002
      weight_decay: 0
      betas: [0.9, 0.99]
    ]
    scheduler:[
      type: MultiStepLR
      milestones: [250000, 400000, 450000, 475000]
      gamma: 0.5
    ]
    total_iter: 500000
    warmup_iter: -1
    pixel_opt:[
      type: L1Loss
      loss_weight: 1.0
      reduction: mean
    ]
    perceptual_opt:[
      type: PerceptualLoss
      layer_weights:[
        conv5_4: 1
      ]
      vgg_type: vgg19
      use_input_norm: True
      range_norm: False
      perceptual_weight: 1.0
      style_weight: 0
      criterion: l1
    ]
  ]
  val:[
    val_freq: 5000.0
    save_img: False
    metrics:[
      psnr:[
        type: calculate_psnr
        crop_border: 0
        test_y_channel: False
      ]
    ]
  ]
  logger:[
    print_freq: 100
    save_checkpoint_freq: 5000.0
    use_tb_logger: True
    wandb:[
      project: None
      resume_id: None
    ]
  ]
  dist_params:[
    backend: nccl
    port: 29500
  ]
  dist: False
  rank: 0
  world_size: 1
  auto_resume: False
  is_train: True
  root_path: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5

