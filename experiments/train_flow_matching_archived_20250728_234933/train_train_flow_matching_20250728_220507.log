2025-07-28 22:05:07,561 INFO: 
                ____                _       _____  ____
               / __ ) ____ _ _____ (_)_____/ ___/ / __ \
              / __  |/ __ `// ___// // ___/\__ \ / /_/ /
             / /_/ // /_/ /(__  )/ // /__ ___/ // _, _/
            /_____/ \__,_//____//_/ \___//____//_/ |_|
     ______                   __   __                 __      __
    / ____/____   ____   ____/ /  / /   __  __ _____ / /__   / /
   / / __ / __ \ / __ \ / __  /  / /   / / / // ___// //_/  / /
  / /_/ // /_/ // /_/ // /_/ /  / /___/ /_/ // /__ / /<    /_/
  \____/ \____/ \____/ \____/  /_____/\____/ \___//_/|_|  (_)
    
Version Information: 
	BasicSR: 1.4.2
	PyTorch: 2.4.1+cu121
	TorchVision: 0.19.1+cu121
2025-07-28 22:05:07,562 INFO: 
  name: train_flow_matching
  model_type: PolarFree_FlowMatching
  scale: 1
  num_gpu: 1
  manual_seed: 100
  datasets:[
    train:[
      name: TrainSet
      type: PairedImagePolarDataset
      dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
      dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
      filename_tmpl: {}
      io_backend:[
        type: disk
      ]
      test_scenes: []
      easy_data_ratio: 1
      hard_data_ratio: 3
      use_shuffle: True
      num_worker_per_gpu: 6
      batch_size_per_gpu: 2
      dataset_enlarge_ratio: 1
      prefetch_mode: None
      gt_size: 256
      phase: train
      scale: 1
    ]
    val:[
      name: ValSet
      type: PairedImagePolarDataset
      dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
      dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
      filename_tmpl: {}
      io_backend:[
        type: disk
      ]
      test_scenes: ['00', '01', '02', '03', '04', '05', '06', '07']
      phase: val
      scale: 1
    ]
  ]
  network_g:[
    type: Transformer
    inp_channels: 3
    out_channels: 3
    dim: 48
    num_blocks: [3, 4, 4, 4]
    num_refinement_blocks: 4
    heads: [1, 2, 4, 8]
    ffn_expansion_factor: 2.66
    bias: False
    LayerNorm_type: WithBias
    dual_pixel_task: False
    embed_dim: 64
    group: 4
  ]
  network_le:[
    type: latent_encoder_gelu
    in_chans: 12
    embed_dim: 64
    block_num: 6
    group: 4
    stage: 1
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_flow:[
    type: PolarFlowMatching
    in_channels: 64
    out_channels: 64
    condition_dim: 64
    model_channels: 128
    num_res_blocks: 2
    channel_mult: [1, 2, 4, 8]
    attention_resolutions: [16, 8]
    num_heads: 8
    dropout: 0.1
    sigma: 0.0
    use_ot: False
  ]
  network_flow_condition:[
    type: latent_encoder_gelu
    in_chans: 9
    embed_dim: 64
    block_num: 4
    group: 4
    stage: 2
    patch_expansion: 0.5
    channel_expansion: 2
  ]
  flow_matching:[
    num_steps: 50
    solver: euler
    sigma: 0.0
    loss_weight: 1.0
    use_conditioning: True
  ]
  path:[
    pretrain_network_g: None
    param_key_g: params
    strict_load_g: True
    pretrain_network_le: None
    param_key_le: params
    strict_load_le: True
    pretrain_network_flow: None
    param_key_flow: params
    strict_load_flow: False
    pretrain_network_flow_condition: None
    param_key_flow_condition: params
    strict_load_flow_condition: True
    resume_state: None
    experiments_root: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/train_flow_matching
    models: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/train_flow_matching/models
    training_states: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/train_flow_matching/training_states
    log: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/train_flow_matching
    visualization: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5/experiments/train_flow_matching/visualization
  ]
  cache_dir: ~/.cache/polarfree
  train:[
    total_iter: 300000
    warmup_iter: -1
    use_grad_clip: True
    freeze_stage1: False
    scheduler:[
      type: CosineAnnealingRestartCyclicLR
      periods: [100000, 200000]
      restart_weights: [1, 1]
      eta_mins: [0.0002, 1e-06]
    ]
    mixing_augs:[
      mixup: False
      mixup_beta: 1.2
      use_identity: True
    ]
    optim_g:[
      type: Adam
      lr: 0.0002
      weight_decay: 0.0001
      betas: [0.9, 0.999]
    ]
    pixel_opt:[
      type: L1Loss
      loss_weight: 1.0
      reduction: mean
    ]
    flow_opt:[
      type: MSELoss
      loss_weight: 1.0
      reduction: mean
    ]
    tv_opt:[
      loss_weight: 0.0005
    ]
    vgg_opt:[
      loss_weight: 0.02
    ]
  ]
  val:[
    val_freq: 5000.0
    save_img: True
    metrics:[
      psnr:[
        type: calculate_psnr
        crop_border: 0
        test_y_channel: False
      ]
      ssim:[
        type: calculate_ssim
        crop_border: 0
        test_y_channel: False
      ]
    ]
  ]
  logger:[
    print_freq: 1000
    save_checkpoint_freq: 10000.0
    use_tb_logger: False
    wandb:[
      project: None
      resume_id: None
    ]
  ]
  dist_params:[
    backend: nccl
    port: 29500
  ]
  dist: False
  rank: 0
  world_size: 1
  auto_resume: False
  is_train: True
  root_path: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v5

2025-07-28 22:05:07,618 INFO: Training statistics:
	Number of train images: 9722
	Dataset enlarge ratio: 1
	Batch size per gpu: 2
	World size (gpu number): 1
	Require iter number per epoch: 4861
	Total epochs: 62; iters: 300000.
2025-07-28 22:05:07,621 INFO: Number of val images/folders in ValSet: 188
