#!/usr/bin/env python3
"""
Test script to verify the condition tensor shape fix.
"""

import torch
import torch.nn as nn
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_condition_shape_fix():
    """Test that the condition tensor shape fix works correctly."""
    print("🧪 Testing condition tensor shape fix...")
    
    try:
        from polarfree.archs.latent_encoder_arch import latent_encoder_gelu
        from polarfree.archs.flow_matching_arch import PolarFlowMatching
        
        # Create networks with same config as training
        net_flow_condition = latent_encoder_gelu(
            in_chans=17,  # RGB(3) + img0(3) + img45(3) + img90(3) + img135(3) + aolp(1) + dolp(1) = 17 channels
            embed_dim=64,
            block_num=4,
            group=4,
            stage=2,
            patch_expansion=0.5,
            channel_expansion=2
        )
        
        net_flow = PolarFlowMatching(
            in_channels=64,
            out_channels=64,
            condition_dim=64,
            model_channels=128,
            num_res_blocks=2,
            channel_mult=[1, 2, 4, 8],
            attention_resolutions=[16, 8],
            num_heads=8,
            dropout=0.1,
            sigma=0.0,
            use_ot=False,
        )
        
        print("✅ Networks created successfully")
        
        # Create dummy input features (7 tensors: RGB + 4 polarization angles + 2 polarization params)
        batch_size = 2
        height, width = 64, 64
        
        input_features = [
            torch.randn(batch_size, 3, height, width),  # RGB
            torch.randn(batch_size, 3, height, width),  # img0
            torch.randn(batch_size, 3, height, width),  # img45
            torch.randn(batch_size, 3, height, width),  # img90
            torch.randn(batch_size, 3, height, width),  # img135
            torch.randn(batch_size, 1, height, width),  # aolp
            torch.randn(batch_size, 1, height, width),  # dolp
        ]
        
        print(f"✅ Created input features: {len(input_features)} tensors")
        
        # Test condition encoding
        condition_input = input_features[:7]  # All 7 features
        condition_raw = net_flow_condition(condition_input)
        print(f"✅ Raw condition shape: {condition_raw.shape}")
        
        # Apply the fix: average pool over spatial dimension
        condition = condition_raw.mean(dim=1)  # [batch_size, embed_dim*4]
        print(f"✅ Pooled condition shape: {condition.shape}")
        
        # Check if projection is needed
        expected_condition_dim = net_flow.unet.condition_embed[0].in_features
        print(f"✅ Expected condition dim: {expected_condition_dim}")
        
        if condition.shape[-1] != expected_condition_dim:
            print(f"⚠️  Need projection: {condition.shape[-1]} -> {expected_condition_dim}")
            condition_proj = nn.Linear(condition.shape[-1], expected_condition_dim)
            condition = condition_proj(condition)
            print(f"✅ Projected condition shape: {condition.shape}")
        
        # Test flow matching forward pass
        x = torch.randn(batch_size, 64, 16, 16)  # Latent features
        t = torch.rand(batch_size)  # Time steps
        
        with torch.no_grad():
            output = net_flow(x, t, condition)
            print(f"✅ Flow matching forward pass: {x.shape} -> {output.shape}")
        
        # Test loss computation
        x0 = torch.randn(batch_size, 64, 16, 16)
        x1 = torch.randn(batch_size, 64, 16, 16)
        
        with torch.no_grad():
            loss = net_flow.compute_loss(x0, x1, condition)
            print(f"✅ Flow matching loss: {loss.item():.4f}")
        
        print("🎉 All tests passed! The condition shape fix works correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = test_condition_shape_fix()
    if success:
        print("\n✅ Condition tensor shape fix is working correctly!")
        print("You can now run the training script without the matrix multiplication error.")
    else:
        print("\n❌ There are still issues with the condition tensor shape fix.")
        print("Please check the error messages above.")
