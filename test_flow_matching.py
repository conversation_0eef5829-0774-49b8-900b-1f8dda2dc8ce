"""
Test script for PolarFree Flow Matching model
Compares performance with original PolarFree models
"""

import argparse
import cv2
import glob
import numpy as np
import os
import torch
import time
from collections import OrderedDict

from basicsr.archs import build_network
from basicsr.utils import imwrite, img2tensor, tensor2img
from polarfree.utils.options import parse_options
from polarfree.models import build_model
import yaml


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        '--opt', 
        type=str, 
        default='options/test/test_flow_matching.yml',
        help='Path to option YAML file.'
    )
    parser.add_argument(
        '--input_path',
        type=str,
        help='Input image path or directory'
    )
    parser.add_argument(
        '--output_path',
        type=str,
        default='results/flow_matching',
        help='Output directory'
    )
    parser.add_argument(
        '--compare_with',
        type=str,
        help='Path to comparison model config (e.g., original PolarFree)'
    )
    parser.add_argument(
        '--num_flow_steps',
        type=int,
        default=50,
        help='Number of flow matching steps'
    )
    parser.add_argument(
        '--flow_solver',
        type=str,
        default='euler',
        choices=['euler', 'rk4', 'dopri5'],
        help='ODE solver for flow matching'
    )
    parser.add_argument(
        '--save_input',
        action='store_true',
        help='Save input images'
    )
    parser.add_argument(
        '--benchmark',
        action='store_true',
        help='Run benchmark tests'
    )
    
    args = parser.parse_args()
    
    # Load configuration
    with open(args.opt, 'r') as f:
        opt = yaml.safe_load(f)
        
    # Override flow matching settings
    if 'flow_matching' not in opt:
        opt['flow_matching'] = {}
    opt['flow_matching']['num_steps'] = args.num_flow_steps
    opt['flow_matching']['solver'] = args.flow_solver
    
    # Create output directory
    os.makedirs(args.output_path, exist_ok=True)
    
    # Build model
    print("Building Flow Matching model...")
    model = build_model(opt)
    
    # Load comparison model if specified
    comparison_model = None
    if args.compare_with:
        print("Building comparison model...")
        with open(args.compare_with, 'r') as f:
            comp_opt = yaml.safe_load(f)
        comparison_model = build_model(comp_opt)
    
    # Get test images
    if args.input_path:
        if os.path.isfile(args.input_path):
            test_paths = [args.input_path]
        else:
            test_paths = sorted(glob.glob(os.path.join(args.input_path, '*')))
    else:
        # Use validation dataset from config
        test_paths = get_test_paths_from_config(opt)
    
    print(f"Found {len(test_paths)} test images")
    
    # Test images
    results = []
    total_time = 0
    
    for i, img_path in enumerate(test_paths):
        print(f"Processing {i+1}/{len(test_paths)}: {os.path.basename(img_path)}")
        
        # Load and preprocess image
        img_data = load_polarization_data(img_path)
        
        # Test Flow Matching model
        start_time = time.time()
        with torch.no_grad():
            model.feed_data(img_data)
            model.test()
            fm_result = model.get_current_visuals()['result']
        fm_time = time.time() - start_time
        total_time += fm_time
        
        # Test comparison model if available
        comp_result = None
        comp_time = 0
        if comparison_model:
            start_time = time.time()
            with torch.no_grad():
                comparison_model.feed_data(img_data)
                comparison_model.test()
                comp_result = comparison_model.get_current_visuals()['result']
            comp_time = time.time() - start_time
        
        # Save results
        img_name = os.path.splitext(os.path.basename(img_path))[0]
        
        # Save Flow Matching result
        fm_img = tensor2img(fm_result)
        fm_save_path = os.path.join(args.output_path, f'{img_name}_flow_matching.png')
        imwrite(fm_img, fm_save_path)
        
        # Save comparison result
        if comp_result is not None:
            comp_img = tensor2img(comp_result)
            comp_save_path = os.path.join(args.output_path, f'{img_name}_comparison.png')
            imwrite(comp_img, comp_save_path)
        
        # Save input if requested
        if args.save_input:
            input_img = tensor2img(img_data['lq_rgb'])
            input_save_path = os.path.join(args.output_path, f'{img_name}_input.png')
            imwrite(input_img, input_save_path)
            
        # Calculate metrics if ground truth available
        metrics = {}
        if 'gt_rgb' in img_data:
            gt_img = tensor2img(img_data['gt_rgb'])
            gt_save_path = os.path.join(args.output_path, f'{img_name}_gt.png')
            imwrite(gt_img, gt_save_path)
            
            # Calculate PSNR and SSIM
            metrics['fm_psnr'] = calculate_psnr(fm_img, gt_img)
            metrics['fm_ssim'] = calculate_ssim(fm_img, gt_img)
            
            if comp_result is not None:
                metrics['comp_psnr'] = calculate_psnr(comp_img, gt_img)
                metrics['comp_ssim'] = calculate_ssim(comp_img, gt_img)
        
        # Store results
        result_info = {
            'image': img_name,
            'fm_time': fm_time,
            'comp_time': comp_time,
            **metrics
        }
        results.append(result_info)
        
        print(f"  Flow Matching time: {fm_time:.3f}s")
        if comp_time > 0:
            print(f"  Comparison time: {comp_time:.3f}s")
        if metrics:
            print(f"  Flow Matching PSNR: {metrics.get('fm_psnr', 'N/A'):.2f}")
            if 'comp_psnr' in metrics:
                print(f"  Comparison PSNR: {metrics['comp_psnr']:.2f}")
    
    # Print summary
    print("\n" + "="*50)
    print("SUMMARY")
    print("="*50)
    print(f"Total images processed: {len(test_paths)}")
    print(f"Average Flow Matching time: {total_time/len(test_paths):.3f}s")
    
    if results and 'fm_psnr' in results[0]:
        avg_fm_psnr = np.mean([r['fm_psnr'] for r in results])
        avg_fm_ssim = np.mean([r['fm_ssim'] for r in results])
        print(f"Average Flow Matching PSNR: {avg_fm_psnr:.2f}")
        print(f"Average Flow Matching SSIM: {avg_fm_ssim:.4f}")
        
        if 'comp_psnr' in results[0]:
            avg_comp_psnr = np.mean([r['comp_psnr'] for r in results])
            avg_comp_ssim = np.mean([r['comp_ssim'] for r in results])
            print(f"Average Comparison PSNR: {avg_comp_psnr:.2f}")
            print(f"Average Comparison SSIM: {avg_comp_ssim:.4f}")
            
            print(f"PSNR improvement: {avg_fm_psnr - avg_comp_psnr:.2f} dB")
            print(f"SSIM improvement: {avg_fm_ssim - avg_comp_ssim:.4f}")
    
    # Save detailed results
    import json
    results_path = os.path.join(args.output_path, 'results.json')
    with open(results_path, 'w') as f:
        json.dump(results, f, indent=2)
    print(f"Detailed results saved to: {results_path}")
    
    # Run benchmark if requested
    if args.benchmark:
        run_benchmark(model, comparison_model, opt)


def load_polarization_data(img_path):
    """
    Load polarization data from image path.
    This is a placeholder - implement according to your data format.
    """
    # Placeholder implementation
    # You need to implement this based on your actual data format
    
    # For now, assume we have RGB image and generate dummy polarization data
    img = cv2.imread(img_path, cv2.IMREAD_COLOR)
    img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
    
    # Convert to tensor
    img_tensor = img2tensor(img, bgr2rgb=False, float32=True)
    
    # Generate dummy polarization data (replace with actual loading)
    h, w = img_tensor.shape[1:]
    
    data = {
        'lq_rgb': img_tensor.unsqueeze(0),
        'lq_img0': img_tensor.unsqueeze(0),
        'lq_img45': img_tensor.unsqueeze(0),
        'lq_img90': img_tensor.unsqueeze(0),
        'lq_img135': img_tensor.unsqueeze(0),
        'lq_aolp': torch.zeros(1, 1, h, w),
        'lq_dolp': torch.zeros(1, 1, h, w),
    }
    
    return data


def get_test_paths_from_config(opt):
    """Get test image paths from configuration."""
    # Placeholder - implement based on your dataset structure
    test_dir = opt['datasets']['test_1']['dataroot_lq']
    test_paths = sorted(glob.glob(os.path.join(test_dir, '*.png')))
    return test_paths


def calculate_psnr(img1, img2):
    """Calculate PSNR between two images."""
    mse = np.mean((img1.astype(np.float64) - img2.astype(np.float64)) ** 2)
    if mse == 0:
        return float('inf')
    return 20 * np.log10(255.0 / np.sqrt(mse))


def calculate_ssim(img1, img2):
    """Calculate SSIM between two images."""
    from skimage.metrics import structural_similarity as ssim
    
    # Convert to grayscale if needed
    if len(img1.shape) == 3:
        img1_gray = cv2.cvtColor(img1, cv2.COLOR_RGB2GRAY)
        img2_gray = cv2.cvtColor(img2, cv2.COLOR_RGB2GRAY)
    else:
        img1_gray = img1
        img2_gray = img2
    
    return ssim(img1_gray, img2_gray, data_range=255)


def run_benchmark(model, comparison_model, opt):
    """Run benchmark tests."""
    print("\n" + "="*50)
    print("BENCHMARK")
    print("="*50)
    
    # Test different flow steps
    flow_steps = [10, 25, 50, 100]
    
    # Create dummy data for benchmarking
    dummy_data = {
        'lq_rgb': torch.randn(1, 3, 256, 256),
        'lq_img0': torch.randn(1, 3, 256, 256),
        'lq_img45': torch.randn(1, 3, 256, 256),
        'lq_img90': torch.randn(1, 3, 256, 256),
        'lq_img135': torch.randn(1, 3, 256, 256),
        'lq_aolp': torch.randn(1, 1, 256, 256),
        'lq_dolp': torch.randn(1, 1, 256, 256),
    }
    
    print("Testing different flow steps:")
    for steps in flow_steps:
        # Update flow steps
        if hasattr(model, 'num_flow_steps'):
            model.num_flow_steps = steps
        
        # Warm up
        with torch.no_grad():
            model.feed_data(dummy_data)
            model.test()
        
        # Benchmark
        times = []
        for _ in range(10):
            start_time = time.time()
            with torch.no_grad():
                model.feed_data(dummy_data)
                model.test()
            times.append(time.time() - start_time)
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        print(f"  {steps} steps: {avg_time:.3f}±{std_time:.3f}s")
    
    # Test different solvers
    solvers = ['euler', 'rk4']
    print("\nTesting different solvers:")
    for solver in solvers:
        if hasattr(model, 'flow_solver'):
            model.flow_solver = solver
        
        times = []
        for _ in range(10):
            start_time = time.time()
            with torch.no_grad():
                model.feed_data(dummy_data)
                model.test()
            times.append(time.time() - start_time)
        
        avg_time = np.mean(times)
        std_time = np.std(times)
        print(f"  {solver}: {avg_time:.3f}±{std_time:.3f}s")


if __name__ == '__main__':
    main()
