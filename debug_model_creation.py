"""
Debug script to test model creation step by step
"""

import torch
import sys
import os
import yaml

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_model_creation():
    """Test model creation step by step."""
    print("🔍 Debugging Model Creation...")
    
    try:
        # Load configuration
        with open('options/train/ours_flow_matching.yml', 'r') as f:
            opt = yaml.safe_load(f)
        print("✅ Configuration loaded successfully")
        
        # Test individual network creation
        # Import polarfree archs to register them
        import polarfree.archs
        from polarfree.archs import build_network
        
        # Test Generator
        print("\n🧪 Testing Generator (Transformer)...")
        try:
            net_g = build_network(opt['network_g'])
            print(f"✅ Generator created: {sum(p.numel() for p in net_g.parameters()):,} parameters")
        except Exception as e:
            print(f"❌ Generator failed: {e}")
            return False
        
        # Test Latent Encoder
        print("\n🧪 Testing Latent Encoder...")
        try:
            net_le = build_network(opt['network_le'])
            print(f"✅ Latent Encoder created: {sum(p.numel() for p in net_le.parameters()):,} parameters")
        except Exception as e:
            print(f"❌ Latent Encoder failed: {e}")
            return False
        
        # Test Flow Matching Network
        print("\n🧪 Testing Flow Matching Network...")
        try:
            net_flow = build_network(opt['network_flow'])
            print(f"✅ Flow Matching Network created: {sum(p.numel() for p in net_flow.parameters()):,} parameters")
        except Exception as e:
            print(f"❌ Flow Matching Network failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test Flow Condition Network
        print("\n🧪 Testing Flow Condition Network...")
        try:
            net_flow_condition = build_network(opt['network_flow_condition'])
            print(f"✅ Flow Condition Network created: {sum(p.numel() for p in net_flow_condition.parameters()):,} parameters")
        except Exception as e:
            print(f"❌ Flow Condition Network failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Test complete model creation
        print("\n🧪 Testing Complete Model Creation...")
        try:
            from polarfree.models import build_model
            
            # Add required fields
            opt['name'] = 'debug_flow_matching'
            opt['model_type'] = 'PolarFree_FlowMatching'
            opt['num_gpu'] = 1
            opt['dist'] = False
            opt['rank'] = 0
            opt['world_size'] = 1
            opt['is_train'] = True
            opt['root_path'] = os.getcwd()
            
            model = build_model(opt)
            print("✅ Complete model created successfully")
            
            # Test data feeding
            print("\n🧪 Testing Data Feeding...")
            dummy_data = {
                'lq_rgb': torch.randn(1, 3, 64, 64),
                'lq_img0': torch.randn(1, 3, 64, 64),
                'lq_img45': torch.randn(1, 3, 64, 64),
                'lq_img90': torch.randn(1, 3, 64, 64),
                'lq_img135': torch.randn(1, 3, 64, 64),
                'lq_aolp': torch.randn(1, 1, 64, 64),
                'lq_dolp': torch.randn(1, 1, 64, 64),
                'gt_rgb': torch.randn(1, 3, 64, 64),
            }
            
            model.feed_data(dummy_data)
            print("✅ Data feeding successful")
            
            # Test forward pass
            print("\n🧪 Testing Forward Pass...")
            model.optimize_parameters(1)
            print("✅ Forward pass and optimization successful")
            
            return True
            
        except Exception as e:
            print(f"❌ Complete model creation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_memory_usage():
    """Test memory usage of the model."""
    print("\n💾 Testing Memory Usage...")
    
    try:
        import psutil
        import gc
        
        # Get initial memory
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"Initial memory: {initial_memory:.1f} MB")
        
        # Load configuration with smaller model
        with open('options/train/ours_flow_matching.yml', 'r') as f:
            opt = yaml.safe_load(f)
        
        # Reduce model size for testing
        opt['network_g']['dim'] = 24
        opt['network_g']['num_blocks'] = [1, 1, 1, 1]
        opt['network_g']['num_refinement_blocks'] = 1
        opt['network_le']['embed_dim'] = 32
        opt['network_le']['block_num'] = 2
        opt['network_flow']['model_channels'] = 64
        opt['network_flow']['in_channels'] = 32
        opt['network_flow']['out_channels'] = 32
        opt['network_flow']['condition_dim'] = 32
        opt['network_flow_condition']['embed_dim'] = 32
        opt['network_flow_condition']['block_num'] = 2
        
        print("✅ Using reduced model size for memory testing")
        
        # Create model
        from polarfree.models import build_model
        opt['name'] = 'debug_memory'
        opt['model_type'] = 'PolarFree_FlowMatching'
        opt['num_gpu'] = 1
        opt['dist'] = False
        opt['rank'] = 0
        opt['world_size'] = 1
        opt['is_train'] = True
        opt['root_path'] = os.getcwd()
        
        model = build_model(opt)
        
        # Get memory after model creation
        gc.collect()
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        model_memory = process.memory_info().rss / 1024 / 1024  # MB
        print(f"Memory after model creation: {model_memory:.1f} MB")
        print(f"Model memory usage: {model_memory - initial_memory:.1f} MB")
        
        return True
        
    except ImportError:
        print("⚠️  psutil not available, skipping memory test")
        return True
    except Exception as e:
        print(f"❌ Memory test failed: {e}")
        return False


def main():
    """Run all debug tests."""
    print("🔍 PolarFree Flow Matching Debug")
    print("=" * 50)
    
    tests = [
        ("Model Creation", test_model_creation),
        ("Memory Usage", test_memory_usage),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 DEBUG SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All debug tests passed! The model should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
