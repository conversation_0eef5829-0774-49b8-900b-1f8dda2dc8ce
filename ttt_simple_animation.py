import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from mpl_toolkits.mplot3d import Axes3D

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation between two states."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

# Animation setup
print("="*60)
print("RECTIFIED FLOW TRAJECTORY ANIMATION")
print("="*60)

# Parameters
n_trajectories = 6
beta_values = [5, 10, 20]
n_time_steps = 150
ts = np.linspace(0, 1, n_time_steps)

# Base states
x0_base = np.array([5.0, 5.0, 5.0])
y0_base = np.zeros(3)
x1_base = np.array([1.0, 1.0, 1.0])

# Generate trajectories
trajectories = []
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

print(f"Generating {n_trajectories} trajectories for β = {beta_values}")

for i, beta in enumerate(beta_values):
    for traj_id in range(n_trajectories // len(beta_values)):
        np.random.seed(traj_id + beta * 10)
        
        # Create varied states
        x0 = x0_base + np.random.normal(0, 0.2, 3)
        y0 = y0_base + np.random.normal(0, 0.1, 3)
        x1 = x1_base + np.random.normal(0, 0.2, 3)
        y1 = np.random.randn(3) * 0.6
        
        # Compute full trajectory
        states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
        
        trajectories.append({
            'beta': beta,
            'states': states,
            'color': colors[len(trajectories) % len(colors)],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1,
            'label': f'β={beta}, Traj {traj_id+1}'
        })

print(f"Generated {len(trajectories)} trajectories")

# Create figure with subplots
fig = plt.figure(figsize=(18, 12))

# 3D plot for x-space
ax1 = fig.add_subplot(2, 3, 1, projection='3d')
ax1.set_title('3D Trajectories in X-Space', fontweight='bold', fontsize=12)
ax1.set_xlabel('x₁')
ax1.set_ylabel('x₂')
ax1.set_zlabel('x₃')

# 3D plot for y-space
ax2 = fig.add_subplot(2, 3, 2, projection='3d')
ax2.set_title('3D Trajectories in Y-Space', fontweight='bold', fontsize=12)
ax2.set_xlabel('y₁')
ax2.set_ylabel('y₂')
ax2.set_zlabel('y₃')

# 2D projection x1-x2
ax3 = fig.add_subplot(2, 3, 3)
ax3.set_title('2D Projection: x₁-x₂', fontweight='bold', fontsize=12)
ax3.set_xlabel('x₁')
ax3.set_ylabel('x₂')
ax3.grid(True, alpha=0.3)

# 2D projection y1-y2
ax4 = fig.add_subplot(2, 3, 4)
ax4.set_title('2D Projection: y₁-y₂', fontweight='bold', fontsize=12)
ax4.set_xlabel('y₁')
ax4.set_ylabel('y₂')
ax4.grid(True, alpha=0.3)

# Time evolution of norms
ax5 = fig.add_subplot(2, 3, 5)
ax5.set_title('State Vector Norms Evolution', fontweight='bold', fontsize=12)
ax5.set_xlabel('Time t')
ax5.set_ylabel('L2 Norm')
ax5.grid(True, alpha=0.3)

# Sigma_y evolution
ax6 = fig.add_subplot(2, 3, 6)
ax6.set_title('σ_y(t) Evolution for Different β', fontweight='bold', fontsize=12)
ax6.set_xlabel('Time t')
ax6.set_ylabel('σ_y(t)')
ax6.grid(True, alpha=0.3)

# Plot sigma_y curves (static)
for beta in beta_values:
    sigma_values = [sigma_y(t, beta) for t in ts]
    ax6.plot(ts, sigma_values, linewidth=3, label=f'β = {beta}')
ax6.legend()
ax6.set_xlim(0, 1)

# Initialize animation elements
lines_3d_x = []
lines_3d_y = []
lines_2d_x = []
lines_2d_y = []
points_3d_x = []
points_3d_y = []
points_2d_x = []
points_2d_y = []
norm_lines = []

# Set up trajectory lines and points
for traj in trajectories:
    # 3D x-space
    line_x, = ax1.plot([], [], [], color=traj['color'], alpha=0.8, linewidth=2)
    point_x, = ax1.plot([], [], [], 'o', color=traj['color'], markersize=8)
    lines_3d_x.append(line_x)
    points_3d_x.append(point_x)
    
    # 3D y-space
    line_y, = ax2.plot([], [], [], color=traj['color'], alpha=0.8, linewidth=2)
    point_y, = ax2.plot([], [], [], 'o', color=traj['color'], markersize=8)
    lines_3d_y.append(line_y)
    points_3d_y.append(point_y)
    
    # 2D projections
    line_2d_x, = ax3.plot([], [], color=traj['color'], alpha=0.8, linewidth=2)
    point_2d_x, = ax3.plot([], [], 'o', color=traj['color'], markersize=8)
    lines_2d_x.append(line_2d_x)
    points_2d_x.append(point_2d_x)
    
    line_2d_y, = ax4.plot([], [], color=traj['color'], alpha=0.8, linewidth=2)
    point_2d_y, = ax4.plot([], [], 'o', color=traj['color'], markersize=8)
    lines_2d_y.append(line_2d_y)
    points_2d_y.append(point_2d_y)
    
    # Norm evolution
    norm_line, = ax5.plot([], [], color=traj['color'], alpha=0.8, linewidth=2, 
                         label=traj['label'])
    norm_lines.append(norm_line)

# Set axis limits
all_states = np.concatenate([traj['states'] for traj in trajectories])
x_states = all_states[:, :3]
y_states = all_states[:, 3:]

x_min, x_max = x_states.min() - 0.5, x_states.max() + 0.5
y_min, y_max = y_states.min() - 0.5, y_states.max() + 0.5

for ax in [ax1]:
    ax.set_xlim(x_min, x_max)
    ax.set_ylim(x_min, x_max)
    ax.set_zlim(x_min, x_max)

for ax in [ax2]:
    ax.set_xlim(y_min, y_max)
    ax.set_ylim(y_min, y_max)
    ax.set_zlim(y_min, y_max)

ax3.set_xlim(x_min, x_max)
ax3.set_ylim(x_min, x_max)
ax4.set_xlim(y_min, y_max)
ax4.set_ylim(y_min, y_max)
ax5.set_xlim(0, 1)

# Add start/end markers
for i, traj in enumerate(trajectories):
    # Start points (green)
    ax1.scatter([traj['x0'][0]], [traj['x0'][1]], [traj['x0'][2]], 
               c='green', s=150, marker='o', alpha=0.9, edgecolors='black')
    ax2.scatter([traj['y0'][0]], [traj['y0'][1]], [traj['y0'][2]], 
               c='green', s=150, marker='o', alpha=0.9, edgecolors='black')
    ax3.scatter([traj['x0'][0]], [traj['x0'][1]], 
               c='green', s=150, marker='o', alpha=0.9, edgecolors='black')
    ax4.scatter([traj['y0'][0]], [traj['y0'][1]], 
               c='green', s=150, marker='o', alpha=0.9, edgecolors='black')
    
    # End points (red)
    ax1.scatter([traj['x1'][0]], [traj['x1'][1]], [traj['x1'][2]], 
               c='red', s=150, marker='X', alpha=0.9, edgecolors='black')
    ax2.scatter([traj['y1'][0]], [traj['y1'][1]], [traj['y1'][2]], 
               c='red', s=150, marker='X', alpha=0.9, edgecolors='black')
    ax3.scatter([traj['x1'][0]], [traj['x1'][1]], 
               c='red', s=150, marker='X', alpha=0.9, edgecolors='black')
    ax4.scatter([traj['y1'][0]], [traj['y1'][1]], 
               c='red', s=150, marker='X', alpha=0.9, edgecolors='black')

# Add legend to norm plot
ax5.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)

# Time display
time_text = fig.text(0.02, 0.95, '', fontsize=16, fontweight='bold', 
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.7))

print("Setting up animation function...")

def animate(frame):
    """Animation function for each frame."""
    current_time = ts[frame]
    time_text.set_text(f'Time: t = {current_time:.3f} | Frame: {frame+1}/{len(ts)}')

    # Update each trajectory
    for i, traj in enumerate(trajectories):
        states = traj['states']

        # Get trajectory up to current frame
        current_states_x = states[:frame+1, :3]
        current_states_y = states[:frame+1, 3:]

        if len(current_states_x) > 0:
            # Update 3D x-space trajectory
            lines_3d_x[i].set_data_3d(current_states_x[:, 0],
                                      current_states_x[:, 1],
                                      current_states_x[:, 2])

            # Update 3D y-space trajectory
            lines_3d_y[i].set_data_3d(current_states_y[:, 0],
                                      current_states_y[:, 1],
                                      current_states_y[:, 2])

            # Update 2D projections
            lines_2d_x[i].set_data(current_states_x[:, 0], current_states_x[:, 1])
            lines_2d_y[i].set_data(current_states_y[:, 0], current_states_y[:, 1])

            # Update current position markers
            if frame < len(states):
                current_pos_x = states[frame, :3]
                current_pos_y = states[frame, 3:]

                points_3d_x[i].set_data_3d([current_pos_x[0]], [current_pos_x[1]], [current_pos_x[2]])
                points_3d_y[i].set_data_3d([current_pos_y[0]], [current_pos_y[1]], [current_pos_y[2]])
                points_2d_x[i].set_data([current_pos_x[0]], [current_pos_x[1]])
                points_2d_y[i].set_data([current_pos_y[0]], [current_pos_y[1]])

            # Update norm evolution
            x_norms = np.linalg.norm(current_states_x, axis=1)
            y_norms = np.linalg.norm(current_states_y, axis=1)
            combined_norms = np.sqrt(x_norms**2 + y_norms**2)

            norm_lines[i].set_data(ts[:frame+1], combined_norms)

    return (lines_3d_x + lines_3d_y + lines_2d_x + lines_2d_y +
            points_3d_x + points_3d_y + points_2d_x + points_2d_y + norm_lines)

# Create and run animation
print("Creating animation...")
anim = animation.FuncAnimation(fig, animate, frames=len(ts),
                              interval=80, blit=False, repeat=True)

plt.tight_layout()

# Try to save animation
print("Attempting to save animation...")
try:
    print("Saving as GIF...")
    anim.save('rectified_flow_simple.gif', writer='pillow', fps=15, dpi=100)
    print("✓ Animation saved as 'rectified_flow_simple.gif'")
except Exception as e:
    print(f"Could not save GIF: {e}")
    try:
        print("Trying MP4...")
        anim.save('rectified_flow_simple.mp4', writer='ffmpeg', fps=15, dpi=100)
        print("✓ Animation saved as 'rectified_flow_simple.mp4'")
    except Exception as e2:
        print(f"Could not save MP4: {e2}")
        print("Will display animation instead...")

# Display animation
print("Displaying animation...")
plt.show()

print("\n" + "="*60)
print("ANIMATION FEATURES:")
print("="*60)
print("✓ Multiple trajectories with different β parameters")
print("✓ 3D visualization in both X and Y spaces")
print("✓ 2D projections for clearer view")
print("✓ Real-time norm evolution tracking")
print("✓ Static σ_y(t) curves for reference")
print("✓ Start markers (green circles) and end markers (red X)")
print("✓ Smooth trajectory evolution showing rectified flow")
print("✓ Time display with current frame information")
print("="*60)
print("Green circles = High-Quality (HQ) starting states")
print("Red X marks = Low-Quality (LQ) ending states")
print("Colored lines = Trajectory paths over time")
print("Moving dots = Current positions at time t")
print("="*60)
