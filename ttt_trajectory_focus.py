import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from matplotlib.patches import FancyBboxPatch

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation between two states."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

# Setup
print("="*70)
print("RECTIFIED FLOW TRAJECTORY ANIMATION - FOCUSED VIEW")
print("="*70)

# Parameters for focused trajectory visualization
n_trajectories = 12
beta_values = [3, 8, 15]  # More diverse beta values
n_time_steps = 200
ts = np.linspace(0, 1, n_time_steps)

# Create more interesting initial and final states
np.random.seed(42)
trajectories = []

# Color schemes for different betas
beta_colors = {
    3: ['#FF6B6B', '#FF8E8E', '#FFB1B1', '#FFD4D4'],   # Red family
    8: ['#4ECDC4', '#70D4CD', '#92DCD6', '#B4E4DF'],   # Teal family  
    15: ['#45B7D1', '#67C5D7', '#89D3DD', '#ABE1E3']   # Blue family
}

print(f"Generating {n_trajectories} trajectories for β = {beta_values}")

traj_id = 0
for beta in beta_values:
    colors = beta_colors[beta]
    n_per_beta = n_trajectories // len(beta_values)
    
    for i in range(n_per_beta):
        # Create more varied and interesting trajectories
        np.random.seed(traj_id + beta * 50)
        
        # More diverse initial states (HQ)
        x0 = np.array([4 + np.random.uniform(-1, 1), 
                      4 + np.random.uniform(-1, 1), 
                      4 + np.random.uniform(-1, 1)])
        y0 = np.random.normal(0, 0.2, 3)
        
        # More diverse final states (LQ)
        x1 = np.array([1 + np.random.uniform(-0.5, 0.5), 
                      1 + np.random.uniform(-0.5, 0.5), 
                      1 + np.random.uniform(-0.5, 0.5)])
        y1 = np.random.normal(0, 1.0, 3)
        
        # Compute trajectory
        states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
        
        trajectories.append({
            'beta': beta,
            'states': states,
            'color': colors[i % len(colors)],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1,
            'id': traj_id
        })
        traj_id += 1

print(f"Generated {len(trajectories)} trajectories")

# Create focused visualization
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
fig.suptitle('Rectified Flow Trajectory Evolution', fontsize=16, fontweight='bold')

ax1 = axes[0, 0]  # X-space 2D projection
ax2 = axes[0, 1]  # Y-space 2D projection  
ax3 = axes[1, 0]  # Combined trajectory paths
ax4 = axes[1, 1]  # Flow dynamics

# Configure axes
ax1.set_title('Main Variables (X-Space): x₁ vs x₂', fontweight='bold')
ax1.set_xlabel('x₁')
ax1.set_ylabel('x₂')
ax1.grid(True, alpha=0.3)

ax2.set_title('Auxiliary Variables (Y-Space): y₁ vs y₂', fontweight='bold')
ax2.set_xlabel('y₁')
ax2.set_ylabel('y₂')
ax2.grid(True, alpha=0.3)

ax3.set_title('Combined State Evolution', fontweight='bold')
ax3.set_xlabel('Combined X-norm')
ax3.set_ylabel('Combined Y-norm')
ax3.grid(True, alpha=0.3)

ax4.set_title('Flow Dynamics Over Time', fontweight='bold')
ax4.set_xlabel('Time t')
ax4.set_ylabel('Distance from Start')
ax4.grid(True, alpha=0.3)

# Set axis limits
all_states = np.concatenate([traj['states'] for traj in trajectories])
x_states = all_states[:, :3]
y_states = all_states[:, 3:]

x_min, x_max = x_states.min() - 0.3, x_states.max() + 0.3
y_min, y_max = y_states.min() - 0.3, y_states.max() + 0.3

ax1.set_xlim(x_min, x_max)
ax1.set_ylim(x_min, x_max)
ax2.set_xlim(y_min, y_max)
ax2.set_ylim(y_min, y_max)

# Initialize animation elements
trajectory_lines = {'ax1': [], 'ax2': [], 'ax3': [], 'ax4': []}
current_points = {'ax1': [], 'ax2': [], 'ax3': [], 'ax4': []}
trail_lines = {'ax1': [], 'ax2': [], 'ax3': []}

# Setup trajectory elements
for traj in trajectories:
    color = traj['color']
    
    # Main trajectory lines (will be updated)
    line1, = ax1.plot([], [], color=color, alpha=0.3, linewidth=1)
    line2, = ax2.plot([], [], color=color, alpha=0.3, linewidth=1)
    line3, = ax3.plot([], [], color=color, alpha=0.3, linewidth=1)
    line4, = ax4.plot([], [], color=color, alpha=0.7, linewidth=2)
    
    trajectory_lines['ax1'].append(line1)
    trajectory_lines['ax2'].append(line2)
    trajectory_lines['ax3'].append(line3)
    trajectory_lines['ax4'].append(line4)
    
    # Current position points
    point1, = ax1.plot([], [], 'o', color=color, markersize=10, markeredgecolor='black', markeredgewidth=1)
    point2, = ax2.plot([], [], 'o', color=color, markersize=10, markeredgecolor='black', markeredgewidth=1)
    point3, = ax3.plot([], [], 'o', color=color, markersize=10, markeredgecolor='black', markeredgewidth=1)
    
    current_points['ax1'].append(point1)
    current_points['ax2'].append(point2)
    current_points['ax3'].append(point3)
    
    # Trail lines (recent history)
    trail1, = ax1.plot([], [], color=color, alpha=0.8, linewidth=3)
    trail2, = ax2.plot([], [], color=color, alpha=0.8, linewidth=3)
    trail3, = ax3.plot([], [], color=color, alpha=0.8, linewidth=3)
    
    trail_lines['ax1'].append(trail1)
    trail_lines['ax2'].append(trail2)
    trail_lines['ax3'].append(trail3)

# Add start and end markers
for traj in trajectories:
    # Start markers (large green circles)
    ax1.scatter(traj['x0'][0], traj['x0'][1], c='green', s=200, marker='o', 
               alpha=0.8, edgecolors='darkgreen', linewidth=2, zorder=10)
    ax2.scatter(traj['y0'][0], traj['y0'][1], c='green', s=200, marker='o', 
               alpha=0.8, edgecolors='darkgreen', linewidth=2, zorder=10)
    
    # End markers (large red X)
    ax1.scatter(traj['x1'][0], traj['x1'][1], c='red', s=300, marker='X', 
               alpha=0.8, edgecolors='darkred', linewidth=2, zorder=10)
    ax2.scatter(traj['y1'][0], traj['y1'][1], c='red', s=300, marker='X', 
               alpha=0.8, edgecolors='darkred', linewidth=2, zorder=10)

# Add beta legends
legend_elements = []
for beta, colors in beta_colors.items():
    legend_elements.append(plt.Line2D([0], [0], color=colors[0], lw=3, label=f'β = {beta}'))

ax1.legend(handles=legend_elements, loc='upper right')

# Time and progress display
time_text = fig.text(0.02, 0.95, '', fontsize=14, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.5", facecolor="lightblue", alpha=0.8))
progress_text = fig.text(0.02, 0.90, '', fontsize=12,
                        bbox=dict(boxstyle="round,pad=0.3", facecolor="lightyellow", alpha=0.8))

print("Setting up animation...")

def animate(frame):
    """Animation function with enhanced trajectory visualization."""
    current_time = ts[frame]
    progress = (frame + 1) / len(ts) * 100

    # Update display texts
    time_text.set_text(f'Time: t = {current_time:.3f}')
    progress_text.set_text(f'Progress: {progress:.1f}% | Frame {frame+1}/{len(ts)}')

    # Trail length (show recent history)
    trail_length = min(20, frame + 1)

    for i, traj in enumerate(trajectories):
        states = traj['states']

        if frame < len(states):
            # Current position
            current_x = states[frame, :3]
            current_y = states[frame, 3:]

            # Full trajectory up to current frame
            traj_x = states[:frame+1, :3]
            traj_y = states[:frame+1, 3:]

            # Recent trail
            trail_start = max(0, frame - trail_length)
            trail_x = states[trail_start:frame+1, :3]
            trail_y = states[trail_start:frame+1, 3:]

            # Update trajectory lines (full path, faded)
            if len(traj_x) > 1:
                trajectory_lines['ax1'][i].set_data(traj_x[:, 0], traj_x[:, 1])
                trajectory_lines['ax2'][i].set_data(traj_y[:, 0], traj_y[:, 1])

                # Combined state space
                x_norms = np.linalg.norm(traj_x, axis=1)
                y_norms = np.linalg.norm(traj_y, axis=1)
                trajectory_lines['ax3'][i].set_data(x_norms, y_norms)

                # Distance from start over time
                start_pos = np.concatenate([traj['x0'], traj['y0']])
                distances = []
                for j in range(len(traj_x)):
                    current_pos = np.concatenate([traj_x[j], traj_y[j]])
                    dist = np.linalg.norm(current_pos - start_pos)
                    distances.append(dist)
                trajectory_lines['ax4'][i].set_data(ts[:frame+1], distances)

            # Update trail lines (recent history, bright)
            if len(trail_x) > 1:
                trail_lines['ax1'][i].set_data(trail_x[:, 0], trail_x[:, 1])
                trail_lines['ax2'][i].set_data(trail_y[:, 0], trail_y[:, 1])

                trail_x_norms = np.linalg.norm(trail_x, axis=1)
                trail_y_norms = np.linalg.norm(trail_y, axis=1)
                trail_lines['ax3'][i].set_data(trail_x_norms, trail_y_norms)

            # Update current position points
            current_points['ax1'][i].set_data([current_x[0]], [current_x[1]])
            current_points['ax2'][i].set_data([current_y[0]], [current_y[1]])

            current_x_norm = np.linalg.norm(current_x)
            current_y_norm = np.linalg.norm(current_y)
            current_points['ax3'][i].set_data([current_x_norm], [current_y_norm])

    # Update axis limits for ax3 dynamically
    if frame > 10:
        all_x_norms = []
        all_y_norms = []
        for traj in trajectories:
            if frame < len(traj['states']):
                x_norms = np.linalg.norm(traj['states'][:frame+1, :3], axis=1)
                y_norms = np.linalg.norm(traj['states'][:frame+1, 3:], axis=1)
                all_x_norms.extend(x_norms)
                all_y_norms.extend(y_norms)

        if all_x_norms and all_y_norms:
            x_margin = (max(all_x_norms) - min(all_x_norms)) * 0.1
            y_margin = (max(all_y_norms) - min(all_y_norms)) * 0.1
            ax3.set_xlim(min(all_x_norms) - x_margin, max(all_x_norms) + x_margin)
            ax3.set_ylim(min(all_y_norms) - y_margin, max(all_y_norms) + y_margin)

    return (list(trajectory_lines['ax1']) + list(trajectory_lines['ax2']) +
            list(trajectory_lines['ax3']) + list(trajectory_lines['ax4']) +
            list(current_points['ax1']) + list(current_points['ax2']) +
            list(current_points['ax3']) +
            list(trail_lines['ax1']) + list(trail_lines['ax2']) + list(trail_lines['ax3']))

# Create animation
print("Creating animation...")
anim = animation.FuncAnimation(fig, animate, frames=len(ts),
                              interval=60, blit=False, repeat=True)

plt.tight_layout()

# Save animation
print("Saving animation...")
try:
    anim.save('rectified_flow_trajectories.gif', writer='pillow', fps=20, dpi=120)
    print("✓ Animation saved as 'rectified_flow_trajectories.gif'")
except Exception as e:
    print(f"Could not save GIF: {e}")
    print("Displaying animation...")

# Show animation
plt.show()

print("\n" + "="*70)
print("TRAJECTORY ANIMATION FEATURES:")
print("="*70)
print("🎯 VISUALIZATION ELEMENTS:")
print("  • Bright trails showing recent trajectory history")
print("  • Faded full paths showing complete evolution")
print("  • Moving dots indicating current positions")
print("  • Green circles = High-Quality starting states")
print("  • Red X marks = Low-Quality target states")
print("")
print("📊 FOUR-PANEL ANALYSIS:")
print("  • Top-left: X-space trajectories (x₁ vs x₂)")
print("  • Top-right: Y-space trajectories (y₁ vs y₂)")
print("  • Bottom-left: Combined norm evolution")
print("  • Bottom-right: Distance from start over time")
print("")
print("🔬 SCIENTIFIC INSIGHTS:")
print("  • Different β values show distinct flow patterns")
print("  • Non-linear auxiliary variable evolution")
print("  • Smooth interpolation between HQ and LQ states")
print("  • Real-time trajectory dynamics visualization")
print("="*70)
