import numpy as np
import matplotlib.pyplot as plt

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation between two states."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

# Setup
print("="*60)
print("RECTIFIED FLOW TRAJECTORY VISUALIZATION")
print("="*60)

# Parameters
n_trajectories = 9
beta_values = [5, 10, 20]
n_time_steps = 100
ts = np.linspace(0, 1, n_time_steps)

# Generate trajectories
trajectories = []
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFD93D', '#6BCF7F', '#A8E6CF', '#FF8B94', '#B4A7D6', '#D4A574']

print(f"Generating trajectories for β = {beta_values}")

traj_id = 0
for beta in beta_values:
    for i in range(3):  # 3 trajectories per beta
        np.random.seed(traj_id + beta * 5)
        
        # Create initial and final states
        x0 = np.array([4.0 + np.random.uniform(-0.5, 0.5), 
                      4.0 + np.random.uniform(-0.5, 0.5), 
                      4.0 + np.random.uniform(-0.5, 0.5)])
        y0 = np.random.normal(0, 0.1, 3)
        
        x1 = np.array([1.0 + np.random.uniform(-0.3, 0.3), 
                      1.0 + np.random.uniform(-0.3, 0.3), 
                      1.0 + np.random.uniform(-0.3, 0.3)])
        y1 = np.random.normal(0, 0.8, 3)
        
        # Compute trajectory
        states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
        
        trajectories.append({
            'beta': beta,
            'states': states,
            'color': colors[traj_id],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1,
            'label': f'β={beta}, T{i+1}'
        })
        traj_id += 1

print(f"Generated {len(trajectories)} trajectories")

# Create visualization
fig, axes = plt.subplots(2, 3, figsize=(18, 12))
fig.suptitle('Rectified Flow Trajectory Analysis', fontsize=16, fontweight='bold')

# Flatten axes for easier indexing
axes = axes.flatten()

# Plot 1: X-space trajectories (x1 vs x2)
ax = axes[0]
ax.set_title('X-Space Trajectories (x₁ vs x₂)', fontweight='bold')
ax.set_xlabel('x₁')
ax.set_ylabel('x₂')
ax.grid(True, alpha=0.3)

for traj in trajectories:
    states = traj['states']
    x_coords = states[:, :3]
    ax.plot(x_coords[:, 0], x_coords[:, 1], color=traj['color'], 
           alpha=0.8, linewidth=2, label=traj['label'])
    
    # Start and end markers
    ax.scatter(traj['x0'][0], traj['x0'][1], c='green', s=100, marker='o', 
              alpha=0.9, edgecolors='darkgreen', zorder=10)
    ax.scatter(traj['x1'][0], traj['x1'][1], c='red', s=120, marker='X', 
              alpha=0.9, edgecolors='darkred', zorder=10)

# Plot 2: Y-space trajectories (y1 vs y2)
ax = axes[1]
ax.set_title('Y-Space Trajectories (y₁ vs y₂)', fontweight='bold')
ax.set_xlabel('y₁')
ax.set_ylabel('y₂')
ax.grid(True, alpha=0.3)

for traj in trajectories:
    states = traj['states']
    y_coords = states[:, 3:]
    ax.plot(y_coords[:, 0], y_coords[:, 1], color=traj['color'], 
           alpha=0.8, linewidth=2)
    
    # Start and end markers
    ax.scatter(traj['y0'][0], traj['y0'][1], c='green', s=100, marker='o', 
              alpha=0.9, edgecolors='darkgreen', zorder=10)
    ax.scatter(traj['y1'][0], traj['y1'][1], c='red', s=120, marker='X', 
              alpha=0.9, edgecolors='darkred', zorder=10)

# Plot 3: State norm evolution
ax = axes[2]
ax.set_title('State Norm Evolution', fontweight='bold')
ax.set_xlabel('Time t')
ax.set_ylabel('L2 Norm')
ax.grid(True, alpha=0.3)

for traj in trajectories:
    states = traj['states']
    x_norms = np.linalg.norm(states[:, :3], axis=1)
    y_norms = np.linalg.norm(states[:, 3:], axis=1)
    
    ax.plot(ts, x_norms, color=traj['color'], alpha=0.7, linewidth=2, linestyle='-')
    ax.plot(ts, y_norms, color=traj['color'], alpha=0.7, linewidth=2, linestyle='--')

ax.text(0.02, 0.98, 'Solid: X-norm\nDashed: Y-norm', transform=ax.transAxes, 
        verticalalignment='top', fontsize=10, 
        bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

# Plot 4: Combined state space
ax = axes[3]
ax.set_title('Combined State Space', fontweight='bold')
ax.set_xlabel('X-norm')
ax.set_ylabel('Y-norm')
ax.grid(True, alpha=0.3)

for traj in trajectories:
    states = traj['states']
    x_norms = np.linalg.norm(states[:, :3], axis=1)
    y_norms = np.linalg.norm(states[:, 3:], axis=1)
    
    ax.plot(x_norms, y_norms, color=traj['color'], alpha=0.8, linewidth=2)
    
    # Start and end points
    ax.scatter(x_norms[0], y_norms[0], c='green', s=100, marker='o', 
              alpha=0.9, edgecolors='darkgreen', zorder=10)
    ax.scatter(x_norms[-1], y_norms[-1], c='red', s=120, marker='X', 
              alpha=0.9, edgecolors='darkred', zorder=10)

# Plot 5: Distance from start
ax = axes[4]
ax.set_title('Distance from Start', fontweight='bold')
ax.set_xlabel('Time t')
ax.set_ylabel('Distance')
ax.grid(True, alpha=0.3)

for traj in trajectories:
    states = traj['states']
    start_pos = np.concatenate([traj['x0'], traj['y0']])
    distances = [np.linalg.norm(state - start_pos) for state in states]
    
    ax.plot(ts, distances, color=traj['color'], alpha=0.8, linewidth=2)

# Plot 6: Sigma_y evolution
ax = axes[5]
ax.set_title('σ_y(t) Evolution for Different β', fontweight='bold')
ax.set_xlabel('Time t')
ax.set_ylabel('σ_y(t)')
ax.grid(True, alpha=0.3)

for beta in beta_values:
    sigma_values = [sigma_y(t, beta) for t in ts]
    ax.plot(ts, sigma_values, linewidth=4, label=f'β = {beta}')

ax.legend(fontsize=12)

# Add overall legend
from matplotlib.lines import Line2D
legend_elements = [
    Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=10, label='HQ Start'),
    Line2D([0], [0], marker='X', color='w', markerfacecolor='red', markersize=10, label='LQ End'),
    Line2D([0], [0], color='gray', lw=3, label='β = 5'),
    Line2D([0], [0], color='gray', lw=3, label='β = 10'),
    Line2D([0], [0], color='gray', lw=3, label='β = 20')
]

# Add legend to first plot
axes[0].legend(handles=legend_elements[:2], loc='upper right', fontsize=10)

plt.tight_layout()

# Save the plot
plt.savefig('rectified_flow_trajectories_simple.png', dpi=300, bbox_inches='tight')
print("✓ Saved trajectory visualization as 'rectified_flow_trajectories_simple.png'")

plt.show()

print("\n" + "="*60)
print("TRAJECTORY VISUALIZATION COMPLETED")
print("="*60)
print("📊 Six-Panel Analysis:")
print("  1. X-Space Trajectories (x₁ vs x₂)")
print("  2. Y-Space Trajectories (y₁ vs y₂)")
print("  3. State Norm Evolution over Time")
print("  4. Combined State Space Dynamics")
print("  5. Distance from Start over Time")
print("  6. σ_y(t) Reference Curves")
print("")
print("🎯 Visual Elements:")
print("  • Green circles = High-Quality (HQ) starting states")
print("  • Red X marks = Low-Quality (LQ) target states")
print("  • Colored lines = Individual trajectory paths")
print("  • Different β values show distinct flow patterns")
print("")
print("🔬 Key Insights:")
print("  • Linear interpolation in X-space (straight paths)")
print("  • Non-linear interpolation in Y-space (curved paths)")
print("  • Higher β values create steeper transitions")
print("  • Smooth rectified flow from HQ to LQ states")
print("="*60)
