"""
Pretrained Model Loader for Flow Matching and Diffusion Models
Supports loading from Hugging Face, local checkpoints, and other sources
"""

import torch
import torch.nn as nn
import os
import logging
from typing import Dict, Optional, Union, Any
from pathlib import Path
import requests
from urllib.parse import urlparse

try:
    from huggingface_hub import hf_hub_download, snapshot_download
    HF_AVAILABLE = True
except ImportError:
    HF_AVAILABLE = False
    logging.warning("huggingface_hub not available. Install with: pip install huggingface_hub")

try:
    from diffusers import DiffusionPipeline, UNet2DModel, UNet2DConditionModel
    DIFFUSERS_AVAILABLE = True
except ImportError:
    DIFFUSERS_AVAILABLE = False
    logging.warning("diffusers not available. Install with: pip install diffusers")


class PretrainedModelLoader:
    """
    Loader for pretrained diffusion and flow matching models.
    Supports multiple sources and formats.
    """
    
    def __init__(self, cache_dir: Optional[str] = None):
        self.cache_dir = cache_dir or os.path.expanduser("~/.cache/polarfree")
        os.makedirs(self.cache_dir, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
    def load_from_huggingface(
        self,
        repo_id: str,
        filename: Optional[str] = None,
        subfolder: Optional[str] = None,
        revision: Optional[str] = None,
        use_auth_token: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Load model from Hugging Face Hub.
        
        Args:
            repo_id: Repository ID (e.g., "runwayml/stable-diffusion-v1-5")
            filename: Specific file to download
            subfolder: Subfolder in the repository
            revision: Git revision (branch, tag, or commit)
            use_auth_token: Authentication token
            
        Returns:
            Dictionary containing model weights and metadata
        """
        if not HF_AVAILABLE:
            raise ImportError("huggingface_hub is required for loading from Hugging Face")
            
        try:
            if filename:
                # Download specific file
                file_path = hf_hub_download(
                    repo_id=repo_id,
                    filename=filename,
                    subfolder=subfolder,
                    revision=revision,
                    cache_dir=self.cache_dir,
                    use_auth_token=use_auth_token,
                )
                
                # Load the file
                if file_path.endswith('.safetensors'):
                    from safetensors.torch import load_file
                    state_dict = load_file(file_path)
                else:
                    state_dict = torch.load(file_path, map_location='cpu')
                    
                return {
                    'state_dict': state_dict,
                    'source': 'huggingface',
                    'repo_id': repo_id,
                    'filename': filename,
                    'path': file_path
                }
            else:
                # Download entire repository
                repo_path = snapshot_download(
                    repo_id=repo_id,
                    revision=revision,
                    cache_dir=self.cache_dir,
                    use_auth_token=use_auth_token,
                )
                
                return {
                    'repo_path': repo_path,
                    'source': 'huggingface',
                    'repo_id': repo_id,
                }
                
        except Exception as e:
            self.logger.error(f"Failed to load from Hugging Face: {e}")
            raise
            
    def load_from_url(self, url: str, filename: Optional[str] = None) -> Dict[str, Any]:
        """
        Load model from URL.
        
        Args:
            url: URL to download from
            filename: Local filename to save as
            
        Returns:
            Dictionary containing model weights and metadata
        """
        if not filename:
            filename = os.path.basename(urlparse(url).path)
            
        local_path = os.path.join(self.cache_dir, filename)
        
        # Download if not exists
        if not os.path.exists(local_path):
            self.logger.info(f"Downloading {url} to {local_path}")
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            with open(local_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    f.write(chunk)
                    
        # Load the file
        if local_path.endswith('.safetensors'):
            from safetensors.torch import load_file
            state_dict = load_file(local_path)
        else:
            state_dict = torch.load(local_path, map_location='cpu')
            
        return {
            'state_dict': state_dict,
            'source': 'url',
            'url': url,
            'path': local_path
        }
        
    def load_from_local(self, path: str) -> Dict[str, Any]:
        """
        Load model from local path.
        
        Args:
            path: Local file path
            
        Returns:
            Dictionary containing model weights and metadata
        """
        if not os.path.exists(path):
            raise FileNotFoundError(f"Model file not found: {path}")
            
        # Load the file
        if path.endswith('.safetensors'):
            from safetensors.torch import load_file
            state_dict = load_file(path)
        else:
            state_dict = torch.load(path, map_location='cpu')
            
        return {
            'state_dict': state_dict,
            'source': 'local',
            'path': path
        }
        
    def load_diffusers_model(
        self,
        repo_id: str,
        component: str = "unet",
        torch_dtype: torch.dtype = torch.float32,
        use_auth_token: Optional[str] = None,
    ) -> nn.Module:
        """
        Load specific component from diffusers pipeline.
        
        Args:
            repo_id: Repository ID
            component: Component to load ('unet', 'vae', 'text_encoder', etc.)
            torch_dtype: Data type for the model
            use_auth_token: Authentication token
            
        Returns:
            Loaded model component
        """
        if not DIFFUSERS_AVAILABLE:
            raise ImportError("diffusers is required for loading diffusers models")
            
        try:
            # Load the pipeline
            pipeline = DiffusionPipeline.from_pretrained(
                repo_id,
                torch_dtype=torch_dtype,
                use_auth_token=use_auth_token,
                cache_dir=self.cache_dir,
            )
            
            # Extract the requested component
            if hasattr(pipeline, component):
                model = getattr(pipeline, component)
                self.logger.info(f"Loaded {component} from {repo_id}")
                return model
            else:
                raise ValueError(f"Component '{component}' not found in pipeline")
                
        except Exception as e:
            self.logger.error(f"Failed to load diffusers model: {e}")
            raise


class ModelAdapter:
    """
    Adapter for converting pretrained models to PolarFree format.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def adapt_unet_to_flow_matching(
        self,
        unet_state_dict: Dict[str, torch.Tensor],
        target_model: nn.Module,
        strict: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        Adapt U-Net weights to flow matching model.
        
        Args:
            unet_state_dict: Source U-Net state dict
            target_model: Target flow matching model
            strict: Whether to require exact key matching
            
        Returns:
            Adapted state dict
        """
        adapted_dict = {}
        target_keys = set(target_model.state_dict().keys())
        
        # Key mapping rules
        key_mappings = {
            # Time embedding
            'time_embed.0.weight': 'unet.time_embed.0.weight',
            'time_embed.0.bias': 'unet.time_embed.0.bias',
            'time_embed.2.weight': 'unet.time_embed.2.weight',
            'time_embed.2.bias': 'unet.time_embed.2.bias',
            
            # Input blocks
            'input_blocks.0.0.weight': 'unet.input_blocks.0.weight',
            'input_blocks.0.0.bias': 'unet.input_blocks.0.bias',
            
            # Output projection
            'out.0.weight': 'unet.out.0.weight',
            'out.0.bias': 'unet.out.0.bias',
            'out.2.weight': 'unet.out.2.weight',
            'out.2.bias': 'unet.out.2.bias',
        }
        
        # Apply mappings
        for source_key, target_key in key_mappings.items():
            if source_key in unet_state_dict and target_key in target_keys:
                adapted_dict[target_key] = unet_state_dict[source_key]
                self.logger.debug(f"Mapped {source_key} -> {target_key}")
                
        # Handle remaining keys with pattern matching
        for source_key, tensor in unet_state_dict.items():
            # Skip already mapped keys
            if any(source_key == sk for sk in key_mappings.keys()):
                continue
                
            # Try to find matching target key
            potential_targets = [tk for tk in target_keys if self._keys_match(source_key, tk)]
            
            if len(potential_targets) == 1:
                target_key = potential_targets[0]
                if tensor.shape == target_model.state_dict()[target_key].shape:
                    adapted_dict[target_key] = tensor
                    self.logger.debug(f"Auto-mapped {source_key} -> {target_key}")
                else:
                    self.logger.warning(f"Shape mismatch for {source_key}: {tensor.shape} vs {target_model.state_dict()[target_key].shape}")
            elif len(potential_targets) > 1:
                self.logger.warning(f"Multiple potential targets for {source_key}: {potential_targets}")
            else:
                if not strict:
                    self.logger.debug(f"No target found for {source_key}")
                    
        # Fill missing keys with random initialization
        target_dict = target_model.state_dict()
        for key in target_keys:
            if key not in adapted_dict:
                adapted_dict[key] = target_dict[key]
                self.logger.debug(f"Using random initialization for {key}")
                
        self.logger.info(f"Adapted {len(adapted_dict)} parameters")
        return adapted_dict
        
    def _keys_match(self, source_key: str, target_key: str) -> bool:
        """Check if two keys are likely to correspond to the same parameter."""
        # Remove common prefixes
        source_clean = source_key.replace('unet.', '').replace('model.', '')
        target_clean = target_key.replace('unet.', '').replace('model.', '')
        
        # Simple substring matching
        return source_clean in target_clean or target_clean in source_clean
        
    def resize_position_embeddings(
        self,
        pos_embed: torch.Tensor,
        target_size: int
    ) -> torch.Tensor:
        """
        Resize position embeddings to target size.
        
        Args:
            pos_embed: Original position embeddings
            target_size: Target sequence length
            
        Returns:
            Resized position embeddings
        """
        if pos_embed.size(1) == target_size:
            return pos_embed
            
        self.logger.info(f"Resizing position embeddings from {pos_embed.size(1)} to {target_size}")
        
        # Interpolate position embeddings
        pos_embed_resized = torch.nn.functional.interpolate(
            pos_embed.unsqueeze(0).transpose(1, 2),
            size=target_size,
            mode='linear',
            align_corners=False
        ).transpose(1, 2).squeeze(0)
        
        return pos_embed_resized


# Predefined model configurations
PRETRAINED_CONFIGS = {
    "stable-diffusion-v1-5": {
        "repo_id": "runwayml/stable-diffusion-v1-5",
        "component": "unet",
        "type": "diffusers",
    },
    "stable-diffusion-v2-1": {
        "repo_id": "stabilityai/stable-diffusion-2-1",
        "component": "unet", 
        "type": "diffusers",
    },
    "lfm-celeba": {
        "url": "https://drive.google.com/drive/folders/1tbd1t0Yt3ix1v_OCGWJ7xyeubhCi99ql",
        "filename": "model_475.pth",
        "type": "lfm",
    },
    "lfm-ffhq": {
        "url": "https://drive.google.com/drive/folders/1jn6xHlaQ72hKk9RtJKo5lvr7SvYMCobU",
        "filename": "model_475.pth", 
        "type": "lfm",
    },
}


def load_pretrained_model(
    model_name: str,
    target_model: nn.Module,
    cache_dir: Optional[str] = None,
    strict: bool = False,
    **kwargs
) -> nn.Module:
    """
    Convenience function to load and adapt pretrained models.
    
    Args:
        model_name: Name of pretrained model or path/URL
        target_model: Target model to load weights into
        cache_dir: Cache directory for downloads
        strict: Whether to require exact key matching
        **kwargs: Additional arguments for loading
        
    Returns:
        Model with loaded pretrained weights
    """
    loader = PretrainedModelLoader(cache_dir)
    adapter = ModelAdapter()
    
    # Check if it's a predefined config
    if model_name in PRETRAINED_CONFIGS:
        config = PRETRAINED_CONFIGS[model_name]
        
        if config["type"] == "diffusers":
            # Load from diffusers
            pretrained_model = loader.load_diffusers_model(
                config["repo_id"],
                config["component"],
                **kwargs
            )
            state_dict = pretrained_model.state_dict()
        elif config["type"] == "lfm":
            # Load from URL
            model_data = loader.load_from_url(config["url"], config["filename"])
            state_dict = model_data["state_dict"]
        else:
            raise ValueError(f"Unknown model type: {config['type']}")
            
    elif model_name.startswith("http"):
        # Load from URL
        model_data = loader.load_from_url(model_name, **kwargs)
        state_dict = model_data["state_dict"]
        
    elif os.path.exists(model_name):
        # Load from local path
        model_data = loader.load_from_local(model_name)
        state_dict = model_data["state_dict"]
        
    else:
        # Try loading from Hugging Face
        model_data = loader.load_from_huggingface(model_name, **kwargs)
        state_dict = model_data["state_dict"]
        
    # Adapt weights to target model
    adapted_dict = adapter.adapt_unet_to_flow_matching(state_dict, target_model, strict)
    
    # Load adapted weights
    missing_keys, unexpected_keys = target_model.load_state_dict(adapted_dict, strict=False)
    
    if missing_keys:
        logging.warning(f"Missing keys: {missing_keys}")
    if unexpected_keys:
        logging.warning(f"Unexpected keys: {unexpected_keys}")
        
    logging.info(f"Successfully loaded pretrained model: {model_name}")
    return target_model
