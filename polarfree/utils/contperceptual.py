import torch
import torch.nn as nn
from einops import rearrange

from taming.modules.losses.vqperceptual import *  # TODO: taming dependency yes/no?


class LPIPSWithDiscriminator(nn.Module):
    def __init__(self, 
                #  disc_start, 
                 disc_num_layers=3, disc_in_channels=3, disc_factor=1.0, disc_weight=1.0,
                 use_actnorm=False, disc_conditional=False,
                 disc_loss="hinge",  vf_weight=1e2, adaptive_vf=False,
                 cos_margin=0, distmat_margin=0, distmat_weight=1.0, cos_weight=1.0):

        super().__init__()
        assert disc_loss in ["hinge", "vanilla"]
        self.distmat_weight = distmat_weight
        self.cos_weight = cos_weight
        # output log variance

        # self.discriminator = NLayerDiscriminator(input_nc=disc_in_channels,
        #                                          n_layers=disc_num_layers,
        #                                          use_actnorm=use_actnorm
        #                                          ).apply(weights_init)
        # self.discriminator_iter_start = disc_start
        self.disc_loss = hinge_d_loss if disc_loss == "hinge" else vanilla_d_loss
        self.disc_factor = disc_factor
        self.discriminator_weight = disc_weight
        self.disc_conditional = disc_conditional
        self.vf_weight = vf_weight
        self.adaptive_vf = adaptive_vf
        self.cos_margin = cos_margin
        self.distmat_margin = distmat_margin

    # def calculate_adaptive_weight(self, nll_loss, g_loss, last_layer=None):
    #     if last_layer is not None:
    #         nll_grads = torch.autograd.grad(nll_loss, last_layer, retain_graph=True)[0]
    #         g_grads = torch.autograd.grad(g_loss, last_layer, retain_graph=True)[0]
    #     else:
    #         nll_grads = torch.autograd.grad(nll_loss, self.last_layer[0], retain_graph=True)[0]
    #         g_grads = torch.autograd.grad(g_loss, self.last_layer[0], retain_graph=True)[0]

    #     d_weight = torch.norm(nll_grads) / (torch.norm(g_grads) + 1e-4)
    #     d_weight = torch.clamp(d_weight, 0.0, 1e4).detach()
    #     d_weight = d_weight * self.discriminator_weight
    #     return d_weight
    
    def calculate_adaptive_weight_vf(self, nll_loss, vf_loss, last_layer=None):
        if last_layer is not None:
            nll_grads = torch.autograd.grad(nll_loss, last_layer, retain_graph=True)[0]
            vf_grads = torch.autograd.grad(vf_loss, last_layer, retain_graph=True)[0]
        else:
            nll_grads = torch.autograd.grad(nll_loss, self.last_layer[0], retain_graph=True)[0]
            vf_grads = torch.autograd.grad(vf_loss, self.last_layer[0], retain_graph=True)[0]

        vf_weight = torch.norm(nll_grads) / (torch.norm(vf_grads) + 1e-4)
        vf_weight = torch.clamp(vf_weight, 0.0, 1e8).detach()
        vf_weight = vf_weight * self.vf_weight
        return vf_weight

    def forward(self,  optimizer_idx=0,
                # global_step, last_layer=None, cond=None, split="train",
                last_layer=None, cond=None, split="train",
                z=None, aux_feature=None, enc_last_layer=None):

        # now the GAN part
        if optimizer_idx == 0:
            # generator update
            # if cond is None:
            #     assert not self.disc_conditional
            #     logits_fake = self.discriminator(reconstructions.contiguous())
            # else:
            #     assert self.disc_conditional
            #     logits_fake = self.discriminator(torch.cat((reconstructions.contiguous(), cond), dim=1))
            # g_loss = -torch.mean(logits_fake)

            # if self.disc_factor > 0.0:
            #     try:
            #         d_weight = self.calculate_adaptive_weight(nll_loss, g_loss, last_layer=last_layer)
            #     except RuntimeError:
            #         assert not self.training
            #         d_weight = torch.tensor(0.0)
            # else:
            #     d_weight = torch.tensor(0.0)

            d_weight = 1

            # vf loss
            if z is not None and aux_feature is not None:
                z_flat = rearrange(z, 'b c h w -> b c (h w)')
                aux_feature_flat = rearrange(aux_feature, 'b c h w -> b c (h w)')
                z_norm = torch.nn.functional.normalize(z_flat, dim=1)
                aux_feature_norm = torch.nn.functional.normalize(aux_feature_flat, dim=1)
                z_cos_sim = torch.einsum('bci,bcj->bij', z_norm, z_norm)
                aux_feature_cos_sim = torch.einsum('bci,bcj->bij', aux_feature_norm, aux_feature_norm)
                diff = torch.abs(z_cos_sim - aux_feature_cos_sim)
                vf_loss_1 = torch.nn.functional.relu(diff-self.distmat_margin).mean()
                vf_loss_2 = torch.nn.functional.relu(1 - self.cos_margin - torch.nn.functional.cosine_similarity(aux_feature, z)).mean()
                vf_loss = vf_loss_1 * self.distmat_weight + vf_loss_2 * self.cos_weight
            else:
                vf_loss = None

            # disc_factor = adopt_weight(self.disc_factor, global_step, threshold=self.discriminator_iter_start)
            if vf_loss is not None:
                if self.adaptive_vf:
                    try:
                        vf_weight = self.calculate_adaptive_weight_vf(nll_loss, vf_loss, last_layer=enc_last_layer)
                    except RuntimeError:
                        assert not self.training
                        vf_weight = torch.tensor(0.0)
                else:
                    vf_weight = self.vf_weight
                # loss = d_weight * disc_factor * g_loss + vf_weight * vf_loss
                loss = vf_weight * vf_loss
            else:
                loss = d_weight * disc_factor * g_loss

            log = {"{}/total_loss".format(split): loss.clone().detach().mean(), 
                #    "{}/d_weight".format(split): d_weight,#.detach(),
                #    "{}/disc_factor".format(split): torch.tensor(disc_factor),
                #    "{}/g_loss".format(split): g_loss.detach().mean(),
                   }
            if vf_loss is not None:
                log["{}/vf_loss".format(split)] = vf_loss.detach().mean()
                if not isinstance(vf_weight, float):
                    log["{}/vf_weight".format(split)] = vf_weight#.detach()
                else:
                    log["{}/vf_weight".format(split)] = torch.tensor(vf_weight)
            return loss#, log

        # if optimizer_idx == 1:
        #     # second pass for discriminator update
        #     if cond is None:
        #         logits_real = self.discriminator(inputs.contiguous().detach())
        #         logits_fake = self.discriminator(reconstructions.contiguous().detach())
        #     else:
        #         logits_real = self.discriminator(torch.cat((inputs.contiguous().detach(), cond), dim=1))
        #         logits_fake = self.discriminator(torch.cat((reconstructions.contiguous().detach(), cond), dim=1))

        #     disc_factor = adopt_weight(self.disc_factor, global_step, threshold=self.discriminator_iter_start)
        #     d_loss = disc_factor * self.disc_loss(logits_real, logits_fake)

        #     log = {"{}/disc_loss".format(split): d_loss.clone().detach().mean(),
        #            "{}/logits_real".format(split): logits_real.detach().mean(),
        #            "{}/logits_fake".format(split): logits_fake.detach().mean()
        #            }
        #     return d_loss, log

if __name__ == "__main__":
    import torch
    from torch.optim import Adam

    # 初始化模型
    model = LPIPSWithDiscriminator(
        disc_start=1,
        disc_num_layers=3,
        disc_in_channels=64,
        disc_factor=0.0,
        disc_weight=0.5,
        use_actnorm=False,
        disc_conditional=False,
        disc_loss="hinge",
        vf_weight=0.1,
        adaptive_vf=False,
        cos_margin=0.5,
        distmat_margin=0.25,
        distmat_weight=1.0,
        cos_weight=1.0
    )

    # 随机生成输入数据
    batch_size = 2
    channels = 64
    height = 64
    width = 64

    inputs = torch.rand(batch_size, channels, height, width)
    reconstructions = torch.rand(batch_size, channels, height, width)
    optimizer_idx = 0
    global_step = 500
    last_layer = None
    cond = None
    split = "train"
    weights = torch.rand(1)
    z = torch.rand(batch_size, channels, height, width)
    aux_feature = torch.rand(batch_size, channels, height, width)
    enc_last_layer = None

    # 前向传播
    loss, log = model(
        inputs=inputs,
        reconstructions=reconstructions,
        optimizer_idx=optimizer_idx,
        global_step=global_step,
        last_layer=last_layer,
        cond=cond,
        split=split,
        z=z,
        aux_feature=aux_feature,
        enc_last_layer=enc_last_layer
    )

    # 打印结果
    print("Loss:", loss)
    print("Log:", log)