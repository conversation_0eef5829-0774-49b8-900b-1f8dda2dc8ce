import torch
import torch.nn as nn
from einops import rearrange
import torch.nn.functional as F
from basicsr.utils.registry import ARCH_REGISTRY
from polarfree.archs.common import subnet

device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
# device = "cpu"

class Lap_Pyramid_Bicubic(nn.Module):
    """

    """
    def __init__(self, num_high=3):
        super(Lap_Pyramid_Bicubic, self).__init__()

        self.interpolate_mode = 'bicubic'
        self.num_high = num_high

    def pyramid_decom(self, img):
        current = img
        pyr = []
        for i in range(self.num_high):
            down = nn.functional.interpolate(current, size=(current.shape[2] // 2, current.shape[3] // 2), mode=self.interpolate_mode, align_corners=True)
            up = nn.functional.interpolate(down, size=(current.shape[2], current.shape[3]), mode=self.interpolate_mode, align_corners=True)
            diff = current - up
            pyr.append(diff)
            current = down
        pyr.append(current)
        return pyr

    def pyramid_recons(self, pyr):
        image = pyr[-1]
        for level in reversed(pyr[:-1]):
            image = F.interpolate(image, size=(level.shape[2], level.shape[3]), mode=self.interpolate_mode, align_corners=True) + level
        return image
    

class InvBlockExp(nn.Module):
    def __init__(self, subnet_constructor, channel_num, channel_split_num, clamp=1.):
        super(InvBlockExp, self).__init__()

        self.split_len1 = channel_num #3
        self.split_len2 = channel_split_num #12-3
        self.clamp = clamp

        self.F = subnet_constructor(self.split_len2, self.split_len1)
        self.G = subnet_constructor(self.split_len1, self.split_len2)
        self.H = subnet_constructor(self.split_len1, self.split_len2)

    def forward(self, x, rev=False):
        x1, x2 = (x.narrow(1, 0, self.split_len1), x.narrow(1, self.split_len1, self.split_len2))
        if not rev:
            y1 = x1 + self.F(x2)
            self.s = self.clamp * (torch.sigmoid(self.H(y1)) * 2 - 1)
            y2 = x2.mul(torch.exp(self.s)) + self.G(y1)
        else:
            self.s = self.clamp * (torch.sigmoid(self.H(x1)) * 2 - 1)
            y2 = (x2 - self.G(x1)).div(torch.exp(self.s))
            y1 = x1 - self.F(y2)

        return torch.cat((y1, y2), 1)

    def jacobian(self, x, rev=False):
        if not rev:
            jac = torch.sum(self.s)
        else:
            jac = -torch.sum(self.s)

        return jac / x.shape[0]


class InvBlockExpTail(nn.Module):
    def __init__(self, subnet_constructor, channel_num, channel_split_num, clamp=1.):
        super(InvBlockExpTail, self).__init__()

        self.split_len1 = channel_num #3
        self.split_len2 = channel_split_num # 12-3

        self.clamp = clamp

        self.F = subnet_constructor(self.split_len2, self.split_len1)
        self.G = subnet_constructor(self.split_len1, self.split_len2)
        self.H = subnet_constructor(self.split_len1, self.split_len2)

    def forward(self, x, rev=False):
        # x1, x2 = (x.narrow(1, 0, self.split_len1), x.narrow(1, self.split_len1, self.split_len2))
        x1 = x
        x2 = torch.cat([x,x,x],1)
        print(x1.shape, x2.shape)
        if not rev:
            y1 = x1 + self.F(x2)
            self.s = self.clamp * (torch.sigmoid(self.H(y1)) * 2 - 1)
            y2 = x2.mul(torch.exp(self.s)) + self.G(y1)
        else:
            self.s = self.clamp * (torch.sigmoid(self.H(x1)) * 2 - 1)
            y2 = (x2 - self.G(x1)).div(torch.exp(self.s))
            y1 = x1 - self.F(y2)
        return torch.cat((y1, y2), 1)

    def jacobian(self, x, rev=False):
        if not rev:
            jac = torch.sum(self.s)
        else:
            jac = -torch.sum(self.s)

        return jac / x.shape[0]


class InvBlockExp_RNVP(nn.Module):
    def __init__(self, subnet_constructor, channel_num, channel_split_num, clamp=1., clamp_activation='ATAN'):
        super(InvBlockExp_RNVP, self).__init__()

        self.split_len1 = channel_num #3
        self.split_len2 = channel_split_num # 12-3

        self.clamp = clamp

        self.subnet_s1 = subnet_constructor(self.split_len1, self.split_len2)
        self.subnet_t1 = subnet_constructor(self.split_len1, self.split_len2)
        self.subnet_s2 = subnet_constructor(self.split_len2, self.split_len1)
        self.subnet_t2 = subnet_constructor(self.split_len2, self.split_len1)

        if isinstance(clamp_activation, str):
            if clamp_activation == "ATAN":
                self.f_clamp = (lambda u: 0.636 * torch.atan(u))
            elif clamp_activation == "TANH":
                self.f_clamp = torch.tanh
            elif clamp_activation == "SIGMOID":
                self.f_clamp = (lambda u: 2. * (torch.sigmoid(u) - 0.5))
            else:
                raise ValueError(f'Unknown clamp activation "{clamp_activation}"')
        else:
            self.f_clamp = clamp_activation

    def forward(self, x, rev=False):
        x1, x2 = (x.narrow(1, 0, self.split_len1), x.narrow(1, self.split_len1, self.split_len2))

        if not rev:
            s2, t2 = self.subnet_s2(x2), self.subnet_t2(x2)
            s2 = self.clamp * self.f_clamp(s2)
            y1 = torch.exp(s2) * x1 + t2

            s1, t1 = self.subnet_s1(y1), self.subnet_t1(y1)
            s1 = self.clamp * self.f_clamp(s1)
            y2 = torch.exp(s1) * x2 + t1

        else:
            s1, t1 = self.subnet_s1(x1), self.subnet_t1(x1)
            s1 = self.clamp * self.f_clamp(s1)
            y2 = (x2-t1)*torch.exp(-s1)

            s2, t2 = self.subnet_s2(y2), self.subnet_t2(y2)
            s2 = self.clamp * self.f_clamp(s2)
            y1 = (x1-t2)*torch.exp(-s2)

        return torch.cat((y1, y2), 1)

    def jacobian(self, x, rev=False):
        if not rev:
            jac = torch.sum(self.s)
        else:
            jac = -torch.sum(self.s)

        return jac / x.shape[0]


class InvBlockExp_RNVP_SGT(nn.Module):
    def __init__(self, subnet_constructor, channel_num, channel_split_num, cond_channel, clamp=1., clamp_activation='ATAN'):
        super(InvBlockExp_RNVP_SGT, self).__init__()

        self.split_len1 = channel_num #3
        self.split_len2 = channel_split_num # 12-3
        self.cond_channel = cond_channel # 12-3

        self.clamp = clamp

        self.subnet_s1 = subnet_constructor(self.split_len1, self.split_len2, self.cond_channel)
        self.subnet_t1 = subnet_constructor(self.split_len1, self.split_len2, self.cond_channel)
        self.subnet_s2 = subnet_constructor(self.split_len2, self.split_len1, self.cond_channel)
        self.subnet_t2 = subnet_constructor(self.split_len2, self.split_len1, self.cond_channel)

        if isinstance(clamp_activation, str):
            if clamp_activation == "ATAN":
                self.f_clamp = (lambda u: 0.636 * torch.atan(u))
            elif clamp_activation == "TANH":
                self.f_clamp = torch.tanh
            elif clamp_activation == "SIGMOID":
                self.f_clamp = (lambda u: 2. * (torch.sigmoid(u) - 0.5))
            else:
                raise ValueError(f'Unknown clamp activation "{clamp_activation}"')
        else:
            self.f_clamp = clamp_activation

    def forward(self, x, cond,  rev=False):
        x1, x2 = (x.narrow(1, 0, self.split_len1), x.narrow(1, self.split_len1, self.split_len2))

        if not rev:
            s2, t2 = self.subnet_s2(x2, cond), self.subnet_t2(x2, cond)
            s2 = self.clamp * self.f_clamp(s2)
            y1 = torch.exp(s2) * x1 + t2

            s1, t1 = self.subnet_s1(y1, cond), self.subnet_t1(y1, cond)
            s1 = self.clamp * self.f_clamp(s1)
            y2 = torch.exp(s1) * x2 + t1

        else:
            s1, t1 = self.subnet_s1(x1, cond), self.subnet_t1(x1, cond)
            s1 = self.clamp * self.f_clamp(s1)
            y2 = (x2-t1)*torch.exp(-s1)

            s2, t2 = self.subnet_s2(y2, cond), self.subnet_t2(y2, cond)
            s2 = self.clamp * self.f_clamp(s2)
            y1 = (x1-t2)*torch.exp(-s2)

        return torch.cat((y1, y2), 1)




def squeeze2d(input, factor=2):
    assert factor >= 1 and isinstance(factor, int)
    if factor == 1:
        return input
    size = input.size()
    B = size[0]
    C = size[1]
    H = size[2]
    W = size[3]
    assert H % factor == 0 and W % factor == 0, "{}".format((H, W, factor))
    x = input.view(B, C, H // factor, factor, W // factor, factor)
    x = x.permute(0, 1, 3, 5, 2, 4).contiguous()
    x = x.view(B, C * factor * factor, H // factor, W // factor)
    return x

def unsqueeze2d(input, factor=2):
    assert factor >= 1 and isinstance(factor, int)
    factor2 = factor ** 2
    if factor == 1:
        return input
    size = input.size()
    B = size[0]
    C = size[1]
    H = size[2]
    W = size[3]
    assert C % (factor2) == 0, "{}".format(C)
    x = input.view(B, C // factor2, factor, factor, H, W)
    x = x.permute(0, 1, 4, 2, 5, 3).contiguous()
    x = x.view(B, C // (factor2), H * factor, W * factor)
    return x

class SqueezeLayer(nn.Module):
    def __init__(self, factor):
        super().__init__()
        self.factor = factor

    def forward(self, input, logdet=None, reverse=False):
        if not reverse:
            output = squeeze2d(input, self.factor)  # Squeeze in forward
            return output
        else:
            output = unsqueeze2d(input, self.factor)
            return output

class HaarDownsampling(nn.Module):
    def __init__(self, factor):
        super(HaarDownsampling, self).__init__()
        self.squeeze = SqueezeLayer(factor=factor)
    def forward(self, x, rev=False):
        if not rev:
            out = self.squeeze(x)
            return out
        else:
            out = self.squeeze(x, reverse=True)
            return out

@ARCH_REGISTRY.register()
class InvNet(nn.Module):
    def __init__(self, subnet_constructor=None, coupling_layers=8, down_num=1):
        super(InvNet, self).__init__()

        if isinstance(subnet_constructor, str):
            # 字符串方式，调用 common.subnet 获取构造器
            self.subnet_ctor = subnet(subnet_constructor)
        else:
            # 已为 callable
            self.subnet_ctor = subnet_constructor


        operations = []
        for _ in range(coupling_layers):
            b = InvBlockExp_RNVP(self.subnet_ctor, 3, 16)
            operations.append(b)
        self.operations = nn.ModuleList(operations)

        # if self.reverse_proj:
        self.linear_proj = torch.nn.Sequential(torch.nn.ConvTranspose2d(256, 16, kernel_size=1),
        # torch.nn.Upsample(size=(16, 16), mode='bilinear', align_corners=False),
        )
        self.down_pool = nn.AdaptiveAvgPool2d((4, 4))

        self.linear_proj_inv =  torch.nn.Sequential(torch.nn.Conv2d(16, 256, kernel_size=1))
    def gaussian_batch(self, dims):
        return torch.randn(tuple(dims)).to(device)

    def forward(self, x, prior=None, rev=False):
        # forward process
        if not rev:
            prior = rearrange(prior, 'b (h w) c -> b c h w', h=4, w=4)  
            prior = self.linear_proj(prior)
            prior = F.interpolate(prior, size=(x.shape[2], x.shape[3]), mode='bilinear', align_corners=False)

            out = torch.cat([x, prior], 1)
            for op in self.operations:
                out = op.forward(out, rev)

            return out[:, :3, :, :], out[:, 3:, :,:]  

        # reverse process
        else:
            imgnoise = self.gaussian_batch(
                [x.shape[0], 16, x.shape[2], x.shape[3]])

            out = torch.cat([x, imgnoise], 1)
            for op in reversed(self.operations):
                out = op.forward(out, rev)
            
            pred_img = out[:,:3, :, :]
            pred_prior = out[:, 3:, :,:]
            # pred_prior = F.interpolate(pred_prior, size=(4, 4),
            #              mode='bilinear', align_corners=False)
            pred_prior = self.down_pool(pred_prior)

            pred_prior = self.linear_proj_inv(pred_prior)
            pred_prior = rearrange(pred_prior, 'b c h w -> b (h w) c')

            return pred_img, pred_prior
        
        
if __name__ =='__main__':
    from common import subnet
    from time import time
    HDR = torch.ones(2, 3, 160, 160).to(device)
    SDR = torch.ones(2, 3, 160, 160).to(device)
    prior = torch.ones(2, 16, 256).to(device)
    model = InvNet('Resnet', 8).to(device)

    start_time = time()
    for _ in range(10):
        out, n = model(HDR, prior, False)
    end_time = time()
    print("Time taken for 10 iterations: ", end_time - start_time)
    out, n= model(HDR, prior, False)
    print(out.shape, n.shape)

    out, prior_= model(SDR, None, True)
    print(out.shape, prior_.shape)

    total_params = sum(p.numel() for p in model.parameters())
    print(total_params/1000**2)
    from thop.profile import profile

    name = "our"
    total_ops, total_params = profile(model, (HDR,prior ))
    print("%s         | %.4f(M)      | %.4f(G)         |" % (name, total_params / (1000 ** 2), total_ops / (1000 ** 3)))