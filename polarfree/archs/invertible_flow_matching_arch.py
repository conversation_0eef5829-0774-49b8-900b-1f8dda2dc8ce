"""
Invertible Flow Matching Architecture
结合可逆神经网络和流匹配的混合架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Tuple, Union
from basicsr.utils.registry import ARCH_REGISTRY
from einops import rearrange

from .InvNet_arch import InvBlockExp_RNVP, HaarDownsampling
from .flow_matching_arch import ConditionalFlowMatching, timestep_embedding
from .common import subnet


class InvertibleFlowBlock(nn.Module):
    """
    结合可逆变换和流匹配的基础块
    """
    
    def __init__(
        self,
        channels: int,
        subnet_constructor,
        time_embed_dim: int = 512,
        condition_dim: int = 0,
        clamp: float = 1.0
    ):
        super().__init__()
        
        # 可逆变换部分
        self.inv_block = InvBlockExp_RNVP(
            subnet_constructor=subnet_constructor,
            channel_num=channels // 2,
            channel_split_num=channels - channels // 2,
            clamp=clamp
        )
        
        # 时间嵌入处理
        self.time_embed_dim = time_embed_dim
        self.time_mlp = nn.Sequential(
            nn.Linear(time_embed_dim, channels),
            nn.SiLU(),
            nn.Linear(channels, channels),
        )
        
        # 条件嵌入处理（如果有）
        if condition_dim > 0:
            self.condition_mlp = nn.Sequential(
                nn.Linear(condition_dim, channels),
                nn.SiLU(),
                nn.Linear(channels, channels),
            )
        else:
            self.condition_mlp = None
            
        # 流匹配的速度预测网络
        self.velocity_net = nn.Sequential(
            nn.Conv2d(channels, channels, 3, padding=1),
            nn.GroupNorm(8, channels),
            nn.SiLU(),
            nn.Conv2d(channels, channels, 3, padding=1),
        )
        
    def forward(
        self, 
        x: torch.Tensor, 
        time_emb: torch.Tensor,
        condition: Optional[torch.Tensor] = None,
        rev: bool = False
    ) -> torch.Tensor:
        """
        前向传播，结合可逆变换和流匹配
        """
        batch_size = x.shape[0]
        
        # 时间嵌入
        t_emb = self.time_mlp(time_emb)
        
        # 条件嵌入
        if condition is not None and self.condition_mlp is not None:
            c_emb = self.condition_mlp(condition)
            emb = t_emb + c_emb
        else:
            emb = t_emb
            
        # 将嵌入添加到特征中
        while len(emb.shape) < len(x.shape):
            emb = emb[..., None]
        x = x + emb
        
        # 可逆变换
        x_inv = self.inv_block(x, rev=rev)
        
        # 流匹配速度预测
        velocity = self.velocity_net(x_inv)
        
        return x_inv + velocity


@ARCH_REGISTRY.register()
class InvertibleFlowMatching(nn.Module):
    """
    可逆流匹配网络：结合可逆神经网络和流匹配的完整架构
    """
    
    def __init__(
        self,
        in_channels: int = 3,
        out_channels: int = 3,
        model_channels: int = 128,
        num_blocks: int = 4,
        condition_dim: int = 64,
        subnet_type: str = 'Resnet',
        time_embed_dim: int = 512,
        use_haar_downsampling: bool = True,
        sigma: float = 0.0,
        clamp: float = 1.0
    ):
        super().__init__()
        
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.model_channels = model_channels
        self.condition_dim = condition_dim
        
        # 子网络构造器
        if isinstance(subnet_type, str):
            self.subnet_ctor = subnet(subnet_type)
        else:
            self.subnet_ctor = subnet_type
            
        # 流匹配组件
        self.flow_matching = ConditionalFlowMatching(sigma=sigma)
        
        # 时间嵌入
        self.time_embed = nn.Sequential(
            nn.Linear(model_channels, time_embed_dim),
            nn.SiLU(),
            nn.Linear(time_embed_dim, time_embed_dim),
        )
        
        # 输入投影
        self.input_proj = nn.Conv2d(in_channels, model_channels, 3, padding=1)
        
        # Haar下采样（可选）
        if use_haar_downsampling:
            self.downsample = HaarDownsampling(factor=2)
            self.upsample = HaarDownsampling(factor=2)  # 用于逆变换
            block_channels = model_channels * 4  # Haar下采样后通道数增加4倍
        else:
            self.downsample = None
            self.upsample = None
            block_channels = model_channels
            
        # 可逆流匹配块
        self.blocks = nn.ModuleList([
            InvertibleFlowBlock(
                channels=block_channels,
                subnet_constructor=self.subnet_ctor,
                time_embed_dim=time_embed_dim,
                condition_dim=condition_dim,
                clamp=clamp
            )
            for _ in range(num_blocks)
        ])
        
        # 输出投影
        if use_haar_downsampling:
            self.output_proj = nn.Conv2d(model_channels * 4, out_channels, 3, padding=1)
        else:
            self.output_proj = nn.Conv2d(model_channels, out_channels, 3, padding=1)

        # 存储配置
        self.use_haar_downsampling = use_haar_downsampling
            
    def forward(
        self, 
        x: torch.Tensor, 
        timesteps: torch.Tensor,
        condition: Optional[torch.Tensor] = None,
        rev: bool = False
    ) -> torch.Tensor:
        """
        前向传播
        
        Args:
            x: 输入张量 [batch_size, channels, height, width]
            timesteps: 时间步 [batch_size]
            condition: 条件张量 [batch_size, condition_dim]
            rev: 是否反向传播（用于可逆变换）
        """
        # 时间嵌入
        time_emb = self.time_embed(timestep_embedding(timesteps, self.model_channels))
        
        # 输入投影
        if not rev:
            h = self.input_proj(x)
            
            # Haar下采样
            if self.downsample is not None:
                h = self.downsample(h, rev=False)
                
            # 通过可逆流匹配块
            for block in self.blocks:
                h = block(h, time_emb, condition, rev=False)
                
            # Haar上采样
            if self.upsample is not None:
                h = self.upsample(h, rev=True)
                
            # 输出投影
            output = self.output_proj(h)
            
        else:
            # 反向过程
            h = self.input_proj(x)
            
            if self.downsample is not None:
                h = self.downsample(h, rev=False)
                
            # 反向通过可逆流匹配块
            for block in reversed(self.blocks):
                h = block(h, time_emb, condition, rev=True)
                
            if self.upsample is not None:
                h = self.upsample(h, rev=True)
                
            output = self.output_proj(h)
            
        return output
        
    def compute_loss(
        self, 
        x0: torch.Tensor, 
        x1: torch.Tensor,
        condition: Optional[torch.Tensor] = None
    ) -> torch.Tensor:
        """计算流匹配损失"""
        return self.flow_matching.compute_loss(self, x0, x1, condition)
        
    def sample(
        self,
        shape: Tuple[int, ...],
        num_steps: int = 50,
        method: str = "euler",
        condition: Optional[torch.Tensor] = None,
        device: str = "cuda"
    ) -> torch.Tensor:
        """
        从噪声采样生成样本
        """
        batch_size = shape[0]
        
        # 初始噪声
        x = torch.randn(shape, device=device)
        
        # 时间步
        dt = 1.0 / num_steps
        
        for i in range(num_steps):
            t = torch.full((batch_size,), i * dt, device=device)
            
            # 预测速度
            with torch.no_grad():
                velocity = self.forward(x, t, condition)
                
            # 欧拉方法积分
            if method == "euler":
                x = x + dt * velocity
            else:
                raise NotImplementedError(f"Solver {method} not implemented")
                
        return x
        
    def encode_decode_cycle(
        self,
        x: torch.Tensor,
        condition: Optional[torch.Tensor] = None
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        编码-解码循环，利用可逆性质
        """
        # 编码（前向）
        t = torch.zeros(x.shape[0], device=x.device)
        encoded = self.forward(x, t, condition, rev=False)
        
        # 解码（反向）
        decoded = self.forward(encoded, t, condition, rev=True)
        
        return encoded, decoded
