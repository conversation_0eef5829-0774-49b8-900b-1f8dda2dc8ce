"""
PolarFree with Invertible Flow Matching Model
结合可逆神经网络和流匹配的偏振图像增强模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict
from basicsr.archs import build_network
from basicsr.losses import build_loss
from basicsr.models.base_model import BaseModel
from basicsr.utils import get_root_logger, imwrite, tensor2img
from basicsr.utils.registry import MODEL_REGISTRY


@MODEL_REGISTRY.register()
class PolarFree_InvertibleFlow(BaseModel):
    """
    PolarFree模型结合可逆流匹配的完整实现
    """

    def __init__(self, opt):
        super(PolarFree_InvertibleFlow, self).__init__(opt)
        self._init_networks(opt)
        
        # 加载预训练模型
        self._load_pretrained_models()
        
        # 设置可逆流匹配
        self._setup_invertible_flow()
        
        if self.is_train:
            self.init_training_settings()

    def _init_networks(self, opt):
        """初始化所有网络"""
        # 主生成网络
        self.net_g = build_network(opt['network_g'])
        self.net_g = self.model_to_device(self.net_g)
        
        # 潜在编码器
        self.net_le = build_network(opt['network_le'])
        self.net_le = self.model_to_device(self.net_le)
        
        # 可逆流匹配网络
        self.net_inv_flow = build_network(opt['network_inv_flow'])
        self.net_inv_flow = self.model_to_device(self.net_inv_flow)
        
        # 条件编码器（可选）
        if opt.get('network_flow_condition'):
            self.net_flow_condition = build_network(opt['network_flow_condition'])
            self.net_flow_condition = self.model_to_device(self.net_flow_condition)
        else:
            self.net_flow_condition = None

    def _load_pretrained_models(self):
        """加载预训练模型"""
        # 加载主网络预训练权重
        pretrained_g_path = self.opt['path'].get('pretrain_network_g', None)
        if pretrained_g_path is not None:
            param_key = self.opt['path'].get('param_key_g', 'params')
            self.load_network(self.net_g, pretrained_g_path, 
                            self.opt['path'].get('strict_load_g', True), param_key)
            
        # 加载潜在编码器预训练权重
        pretrained_le_path = self.opt['path'].get('pretrain_network_le', None)
        if pretrained_le_path is not None:
            param_key = self.opt['path'].get('param_key_le', 'params')
            self.load_network(self.net_le, pretrained_le_path, 
                            self.opt['path'].get('strict_load_le', True), param_key)

    def _setup_invertible_flow(self):
        """设置可逆流匹配参数"""
        self.inv_flow_config = self.opt.get('invertible_flow', {})
        
        # 流匹配参数
        self.num_flow_steps = self.inv_flow_config.get('num_steps', 50)
        self.flow_solver = self.inv_flow_config.get('solver', 'euler')
        self.flow_sigma = self.inv_flow_config.get('sigma', 0.0)
        
        # 训练参数
        self.inv_flow_loss_weight = self.inv_flow_config.get('loss_weight', 1.0)
        self.reconstruction_loss_weight = self.inv_flow_config.get('recon_loss_weight', 1.0)
        self.use_flow_conditioning = self.inv_flow_config.get('use_conditioning', True)

    def init_training_settings(self):
        """初始化训练设置"""
        self.net_g.train()
        self.net_le.train()
        self.net_inv_flow.train()
        if self.net_flow_condition:
            self.net_flow_condition.train()

        train_opt = self.opt['train']

        # 定义损失函数
        if train_opt.get('pixel_opt'):
            self.cri_pix = build_loss(train_opt['pixel_opt']).to(self.device)
        else:
            self.cri_pix = None

        if train_opt.get('perceptual_opt'):
            self.cri_perceptual = build_loss(train_opt['perceptual_opt']).to(self.device)
        else:
            self.cri_perceptual = None

        # 设置优化器
        self.setup_optimizers()
        self.setup_schedulers()

    def setup_optimizers(self):
        """设置优化器"""
        train_opt = self.opt['train']
        
        # 主网络优化器
        optim_params_g = []
        for v in self.net_g.parameters():
            if v.requires_grad:
                optim_params_g.append(v)
        for v in self.net_le.parameters():
            if v.requires_grad:
                optim_params_g.append(v)
                
        # 可逆流匹配网络优化器
        optim_params_flow = []
        for v in self.net_inv_flow.parameters():
            if v.requires_grad:
                optim_params_flow.append(v)
        if self.net_flow_condition:
            for v in self.net_flow_condition.parameters():
                if v.requires_grad:
                    optim_params_flow.append(v)

        optim_type = train_opt['optim_g'].pop('type')
        self.optimizer_g = self.get_optimizer(optim_type, optim_params_g, **train_opt['optim_g'])
        self.optimizer_flow = self.get_optimizer(optim_type, optim_params_flow, **train_opt['optim_g'])
        
        self.optimizers.append(self.optimizer_g)
        self.optimizers.append(self.optimizer_flow)

    def feed_data(self, data):
        """输入数据"""
        self.lq_rgb = data['lq_rgb'].to(self.device)
        self.lq_img0 = data['lq_img0'].to(self.device)
        self.lq_img45 = data['lq_img45'].to(self.device)
        self.lq_img90 = data['lq_img90'].to(self.device)
        self.lq_img135 = data['lq_img135'].to(self.device)
        self.lq_aolp = data['lq_aolp'].to(self.device)
        self.lq_dolp = data['lq_dolp'].to(self.device)
        
        if 'gt_rgb' in data:
            self.gt_rgb = data['gt_rgb'].to(self.device)

    def forward(self):
        """前向传播"""
        # 准备输入特征
        input_features = [
            self.lq_rgb, self.lq_img0, self.lq_img45,
            self.lq_img90, self.lq_img135, self.lq_aolp, self.lq_dolp
        ]
        
        # 阶段1：从潜在编码器获取先验
        if hasattr(self, 'gt_rgb'):
            prior_z = self.net_le(input_features, self.gt_rgb)
        else:
            prior_z = self.net_le(input_features)
            
        # 可逆流匹配增强
        if self.is_train:
            # 训练：计算可逆流匹配损失
            enhanced_prior, self.inv_flow_loss, self.recon_loss = self._invertible_flow_forward(
                prior_z, input_features, training=True
            )
        else:
            # 推理：从可逆流匹配模型采样
            enhanced_prior, self.inv_flow_loss, self.recon_loss = self._invertible_flow_forward(
                prior_z, input_features, training=False
            )
            
        # 阶段2：使用增强的先验生成输出
        self.output = self.net_g(self.lq_rgb, enhanced_prior)

    def _invertible_flow_forward(self, prior_z, input_features, training=True):
        """可逆流匹配前向传播"""
        if training and hasattr(self, 'gt_rgb'):
            # 训练：计算可逆流匹配损失
            target_prior = self.net_le(input_features, self.gt_rgb)

            # 准备条件
            if self.use_flow_conditioning and self.net_flow_condition:
                condition_input = input_features[:7]
                condition_raw = self.net_flow_condition(condition_input)
                # 处理条件张量形状
                condition = condition_raw.mean(dim=1)
                if condition.shape[-1] != self.net_inv_flow.condition_dim:
                    if not hasattr(self, 'condition_proj'):
                        self.condition_proj = nn.Linear(
                            condition.shape[-1], 
                            self.net_inv_flow.condition_dim
                        ).to(condition.device)
                    condition = self.condition_proj(condition)
            else:
                condition = None

            # 计算流匹配损失
            inv_flow_loss = self.net_inv_flow.compute_loss(prior_z, target_prior, condition)

            # 编码-解码重建损失（利用可逆性质）
            encoded, decoded = self.net_inv_flow.encode_decode_cycle(prior_z, condition)
            recon_loss = F.mse_loss(decoded, prior_z)

            # 训练时使用插值的增强先验
            t = torch.rand(prior_z.shape[0], device=prior_z.device)
            enhanced_prior = (1 - t.view(-1, 1, 1, 1)) * prior_z + t.view(-1, 1, 1, 1) * target_prior

            return enhanced_prior, inv_flow_loss, recon_loss
        else:
            # 推理：使用可逆流匹配采样
            if self.use_flow_conditioning and self.net_flow_condition:
                condition_input = input_features[:7]
                condition_raw = self.net_flow_condition(condition_input)
                condition = condition_raw.mean(dim=1)
                if condition.shape[-1] != self.net_inv_flow.condition_dim:
                    if not hasattr(self, 'condition_proj'):
                        self.condition_proj = nn.Linear(
                            condition.shape[-1], 
                            self.net_inv_flow.condition_dim
                        ).to(condition.device)
                    condition = self.condition_proj(condition)
            else:
                condition = None

            # 推理时直接使用原始先验
            enhanced_prior = prior_z

            return enhanced_prior, None, None

    def optimize_parameters(self, current_iter):
        """优化参数"""
        self.optimizer_g.zero_grad()
        self.optimizer_flow.zero_grad()
        
        self.forward()

        l_total = 0
        loss_dict = OrderedDict()

        # 像素损失
        if self.cri_pix:
            l_pix = self.cri_pix(self.output, self.gt_rgb)
            l_total += l_pix
            loss_dict['l_pix'] = l_pix

        # 感知损失
        if self.cri_perceptual:
            l_percep, l_style = self.cri_perceptual(self.output, self.gt_rgb)
            if l_percep is not None:
                l_total += l_percep
                loss_dict['l_percep'] = l_percep
            if l_style is not None:
                l_total += l_style
                loss_dict['l_style'] = l_style

        # 可逆流匹配损失
        if self.inv_flow_loss is not None:
            l_inv_flow = self.inv_flow_loss * self.inv_flow_loss_weight
            l_total += l_inv_flow
            loss_dict['l_inv_flow'] = l_inv_flow

        # 重建损失
        if self.recon_loss is not None:
            l_recon = self.recon_loss * self.reconstruction_loss_weight
            l_total += l_recon
            loss_dict['l_recon'] = l_recon

        l_total.backward()
        self.optimizer_g.step()
        self.optimizer_flow.step()

        self.log_dict = self.reduce_loss_dict(loss_dict)

    def test(self):
        """测试"""
        self.net_g.eval()
        self.net_le.eval()
        self.net_inv_flow.eval()
        if self.net_flow_condition:
            self.net_flow_condition.eval()
            
        with torch.no_grad():
            self.forward()
            
        self.net_g.train()
        self.net_le.train()
        self.net_inv_flow.train()
        if self.net_flow_condition:
            self.net_flow_condition.train()

    def get_current_visuals(self):
        """获取当前可视化结果"""
        out_dict = OrderedDict()
        out_dict['lq'] = self.lq_rgb.detach().cpu()
        out_dict['result'] = self.output.detach().cpu()
        if hasattr(self, 'gt_rgb'):
            out_dict['gt'] = self.gt_rgb.detach().cpu()
        return out_dict
