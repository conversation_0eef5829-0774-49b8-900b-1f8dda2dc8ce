"""
PolarFree Flow Matching Model
Integrates modern flow matching with existing PolarFree architecture
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from collections import OrderedDict
from copy import deepcopy
import numpy as np

from basicsr.archs import build_network
from basicsr.losses import build_loss
from basicsr.metrics import calculate_metric
from basicsr.utils import get_root_logger, imwrite, tensor2img
from basicsr.utils.registry import MODEL_REGISTRY
from basicsr.models.base_model import BaseModel

from polarfree.utils.pretrained_loader import load_pretrained_model


def default(val, d):
    if val is not None:
        return val
    return d() if callable(d) else d


@MODEL_REGISTRY.register()
class PolarFree_FlowMatching(BaseModel):
    """
    PolarFree model with integrated Flow Matching for enhanced performance.
    Combines the two-stage architecture with modern flow matching techniques.
    """

    def __init__(self, opt):
        super(PolarFree_FlowMatching, self).__init__(opt)
        self._init_networks(opt)
        
        # Load pretrained models if specified
        self._load_pretrained_models()
        
        # Setup flow matching
        self._setup_flow_matching()
        
        if self.is_train:
            self.init_training_settings()

    def _init_networks(self, opt):
        """Initialize all networks."""
        # Stage 1 networks (from pretrained)
        self.net_g = build_network(opt['network_g'])
        self.net_g = self.model_to_device(self.net_g)
        
        self.net_le = build_network(opt['network_le'])
        self.net_le = self.model_to_device(self.net_le)
        
        # Flow matching network
        self.net_flow = build_network(opt['network_flow'])
        self.net_flow = self.model_to_device(self.net_flow)
        
        # Condition encoder for flow matching
        if opt.get('network_flow_condition'):
            self.net_flow_condition = build_network(opt['network_flow_condition'])
            self.net_flow_condition = self.model_to_device(self.net_flow_condition)
        else:
            self.net_flow_condition = None
            
        # Print network info
        self.print_network(self.net_g)
        self.print_network(self.net_le)
        self.print_network(self.net_flow)
        if self.net_flow_condition:
            self.print_network(self.net_flow_condition)

    def _load_pretrained_models(self):
        """Load pretrained models."""
        # Load Stage 1 networks
        load_path = self.opt['path'].get('pretrain_network_g', None)
        if load_path is not None:
            param_key = self.opt['path'].get('param_key_g', 'params')
            self.load_network(self.net_g, load_path, self.opt['path'].get('strict_load_g', True), param_key)

        load_path = self.opt['path'].get('pretrain_network_le', None)
        if load_path is not None:
            param_key = self.opt['path'].get('param_key_le', 'params')
            self.load_network(self.net_le, load_path, self.opt['path'].get('strict_load_le', True), param_key)
            
        # Load pretrained flow matching model
        pretrained_flow_path = self.opt['path'].get('pretrain_network_flow', None)
        if pretrained_flow_path is not None:
            if pretrained_flow_path.startswith('pretrained:'):
                # Load from predefined pretrained models
                model_name = pretrained_flow_path.replace('pretrained:', '')
                self.net_flow = load_pretrained_model(
                    model_name, 
                    self.net_flow,
                    cache_dir=self.opt.get('cache_dir'),
                    strict=self.opt['path'].get('strict_load_flow', False)
                )
            else:
                # Load from local path
                param_key = self.opt['path'].get('param_key_flow', 'params')
                self.load_network(self.net_flow, pretrained_flow_path, 
                                self.opt['path'].get('strict_load_flow', True), param_key)

    def _setup_flow_matching(self):
        """Setup flow matching components."""
        self.flow_config = self.opt.get('flow_matching', {})
        
        # Flow matching parameters
        self.num_flow_steps = self.flow_config.get('num_steps', 50)
        self.flow_solver = self.flow_config.get('solver', 'euler')
        self.flow_sigma = self.flow_config.get('sigma', 0.0)
        
        # Training parameters
        self.flow_loss_weight = self.flow_config.get('loss_weight', 1.0)
        self.use_flow_conditioning = self.flow_config.get('use_conditioning', True)

    def init_training_settings(self):
        """Initialize training settings."""
        self.net_g.train()
        self.net_le.train()
        self.net_flow.train()
        if self.net_flow_condition:
            self.net_flow_condition.train()

        train_opt = self.opt['train']

        # Define losses
        if train_opt.get('pixel_opt'):
            self.cri_pix = build_loss(train_opt['pixel_opt']).to(self.device)
        else:
            self.cri_pix = None

        if train_opt.get('perceptual_opt'):
            perceptual_opt = train_opt['perceptual_opt'].copy()
            # Extract loss_weight before building the loss
            self.perceptual_loss_weight = perceptual_opt.pop('loss_weight', 1.0)
            self.cri_perceptual = build_loss(perceptual_opt).to(self.device)
        else:
            self.cri_perceptual = None
            self.perceptual_loss_weight = 0
            
        if train_opt.get('flow_opt'):
            self.cri_flow = build_loss(train_opt['flow_opt']).to(self.device)
        else:
            self.cri_flow = None

        # Additional losses
        if train_opt.get('tv_opt'):
            self.tv_loss_weight = train_opt['tv_opt']['loss_weight']
        else:
            self.tv_loss_weight = 0

        if train_opt.get('vgg_opt'):
            self.vgg_loss_weight = train_opt['vgg_opt']['loss_weight']
        else:
            self.vgg_loss_weight = 0

        if self.cri_pix is None and self.cri_perceptual is None and self.cri_flow is None:
            raise ValueError('Both pixel and perceptual and flow losses are None.')

        # Set up optimizers and schedulers
        self.setup_optimizers()
        self.setup_schedulers()

    def setup_optimizers(self):
        """Setup optimizers."""
        train_opt = self.opt['train']
        
        # Collect all parameters
        optim_params = []
        
        # Stage 1 parameters (may be frozen)
        if train_opt.get('freeze_stage1', False):
            # Freeze stage 1 networks
            for param in self.net_g.parameters():
                param.requires_grad = False
            for param in self.net_le.parameters():
                param.requires_grad = False
        else:
            optim_params.extend(list(self.net_g.parameters()))
            optim_params.extend(list(self.net_le.parameters()))
            
        # Flow matching parameters
        optim_params.extend(list(self.net_flow.parameters()))
        if self.net_flow_condition:
            optim_params.extend(list(self.net_flow_condition.parameters()))

        optim_type = train_opt['optim_g'].pop('type')
        self.optimizer_g = self.get_optimizer(optim_type, optim_params, **train_opt['optim_g'])
        self.optimizers.append(self.optimizer_g)

    def feed_data(self, data):
        """Feed data to the model."""
        self.lq_rgb = data['lq_rgb'].to(self.device)
        self.lq_img0 = data['lq_img0'].to(self.device)
        self.lq_img45 = data['lq_img45'].to(self.device)
        self.lq_img90 = data['lq_img90'].to(self.device)
        self.lq_img135 = data['lq_img135'].to(self.device)
        self.lq_aolp = data['lq_aolp'].to(self.device)
        self.lq_dolp = data['lq_dolp'].to(self.device)

        if 'gt_rgb' in data:
            self.gt_rgb = data['gt_rgb'].to(self.device)

    def optimize_parameters(self, current_iter):
        """Optimize model parameters."""
        self.optimizer_g.zero_grad()
        
        # Forward pass
        self.forward()
        
        # Calculate losses
        l_total = 0
        loss_dict = OrderedDict()
        
        # Pixel loss
        if self.cri_pix:
            l_pix = self.cri_pix(self.output, self.gt_rgb)
            l_total += l_pix
            loss_dict['l_pix'] = l_pix
            
        # Perceptual loss
        if self.cri_perceptual:
            l_percep, l_style = self.cri_perceptual(self.output, self.gt_rgb)
            if l_percep is not None:
                l_percep_weighted = l_percep * self.perceptual_loss_weight
                l_total += l_percep_weighted
                loss_dict['l_percep'] = l_percep_weighted
            if l_style is not None:
                l_style_weighted = l_style * self.perceptual_loss_weight
                l_total += l_style_weighted
                loss_dict['l_style'] = l_style_weighted
                
        # Flow matching loss
        if hasattr(self, 'flow_loss') and self.flow_loss is not None:
            l_flow = self.flow_loss * self.flow_loss_weight
            l_total += l_flow
            loss_dict['l_flow'] = l_flow
            
        # TV loss
        if self.tv_loss_weight > 0:
            l_tv = self.tv_loss_weight * self.calculate_tv_loss(self.output)
            l_total += l_tv
            loss_dict['l_tv'] = l_tv
            
        # VGG loss
        if self.vgg_loss_weight > 0:
            l_vgg = self.vgg_loss_weight * self.calculate_vgg_loss(self.output, self.gt_rgb)
            l_total += l_vgg
            loss_dict['l_vgg'] = l_vgg

        l_total.backward()
        self.optimizer_g.step()

        self.log_dict = self.reduce_loss_dict(loss_dict)

    def forward(self):
        """Forward pass of the model."""
        # Prepare input features
        input_features = [
            self.lq_rgb, self.lq_img0, self.lq_img45,
            self.lq_img90, self.lq_img135, self.lq_aolp, self.lq_dolp
        ]
        
        # Stage 1: Get prior from latent encoder
        if hasattr(self, 'gt_rgb'):
            prior_z = self.net_le(input_features, self.gt_rgb)
        else:
            prior_z = self.net_le(input_features)
            
        # Flow matching enhancement
        if self.is_train:
            # Training: compute flow matching loss
            enhanced_prior, self.flow_loss = self._flow_matching_forward(
                prior_z, input_features, training=True
            )
        else:
            # Inference: sample from flow matching model
            enhanced_prior, self.flow_loss = self._flow_matching_forward(
                prior_z, input_features, training=False
            )
            
        # Stage 2: Generate output using enhanced prior
        self.output = self.net_g(self.lq_rgb, enhanced_prior)

    def _flow_matching_forward(self, prior_z, input_features, training=True):
        """Flow matching forward pass."""
        if training and hasattr(self, 'gt_rgb'):
            # Training: compute flow matching loss
            # Use ground truth as target for flow matching
            target_prior = self.net_le(input_features, self.gt_rgb)

            # Prepare conditioning
            if self.use_flow_conditioning and self.net_flow_condition:
                # For flow condition, use only the first 7 channels (RGB + 4 polarization angles + 2 polarization params)
                condition_input = input_features[:7]  # RGB + 4 polarization angles + 2 polarization params
                condition_raw = self.net_flow_condition(condition_input)
                # Flatten and project to correct dimension: [batch_size, group*group, embed_dim*4] -> [batch_size, condition_dim]
                condition = condition_raw.mean(dim=1)  # Average pool over spatial dimension: [batch_size, embed_dim*4]
                # Project to condition_dim if needed
                if condition.shape[-1] != self.net_flow.unet.condition_embed[0].in_features:
                    # Add a projection layer if dimensions don't match
                    if not hasattr(self, 'condition_proj'):
                        condition_dim = self.net_flow.unet.condition_embed[0].in_features
                        self.condition_proj = torch.nn.Linear(condition.shape[-1], condition_dim).to(condition.device)
                    condition = self.condition_proj(condition)
            else:
                condition = None

            # Compute flow matching loss
            flow_loss = self.net_flow.compute_loss(prior_z, target_prior, condition)

            # For training, we can use the enhanced prior from flow matching
            # Sample a random time step for training
            t = torch.rand(prior_z.shape[0], device=prior_z.device)
            enhanced_prior = (1 - t.view(-1, 1, 1, 1)) * prior_z + t.view(-1, 1, 1, 1) * target_prior

            return enhanced_prior, flow_loss
        else:
            # Inference: sample from flow matching model
            if self.use_flow_conditioning and self.net_flow_condition:
                # For flow condition, use only the first 7 channels (RGB + 4 polarization angles + 2 polarization params)
                condition_input = input_features[:7]  # RGB + 4 polarization angles + 2 polarization params
                condition_raw = self.net_flow_condition(condition_input)
                # Flatten and project to correct dimension: [batch_size, group*group, embed_dim*4] -> [batch_size, condition_dim]
                condition = condition_raw.mean(dim=1)  # Average pool over spatial dimension: [batch_size, embed_dim*4]
                # Project to condition_dim if needed
                if condition.shape[-1] != self.net_flow.unet.condition_embed[0].in_features:
                    # Add a projection layer if dimensions don't match
                    if not hasattr(self, 'condition_proj'):
                        condition_dim = self.net_flow.unet.condition_embed[0].in_features
                        self.condition_proj = torch.nn.Linear(condition.shape[-1], condition_dim).to(condition.device)
                    condition = self.condition_proj(condition)
            else:
                condition = None

            # For inference without ground truth, just use the original prior
            # In a real implementation, you might want to use flow matching sampling here
            enhanced_prior = prior_z

            return enhanced_prior, None

    def calculate_tv_loss(self, img):
        """Calculate total variation loss."""
        tv_h = torch.mean(torch.abs(img[:, :, 1:, :] - img[:, :, :-1, :]))
        tv_w = torch.mean(torch.abs(img[:, :, :, 1:] - img[:, :, :, :-1]))
        return tv_h + tv_w

    def calculate_vgg_loss(self, pred, target):
        """Calculate VGG perceptual loss."""
        # Placeholder - implement VGG loss if needed
        return F.mse_loss(pred, target)

    def test(self):
        """Test function."""
        if hasattr(self, 'net_g_ema'):
            self.net_g_ema.eval()
            self.net_le_ema.eval()
            self.net_flow_ema.eval()
            with torch.no_grad():
                self.forward()
        else:
            self.net_g.eval()
            self.net_le.eval()
            self.net_flow.eval()
            if self.net_flow_condition:
                self.net_flow_condition.eval()
            with torch.no_grad():
                self.forward()
            self.net_g.train()
            self.net_le.train()
            self.net_flow.train()
            if self.net_flow_condition:
                self.net_flow_condition.train()

    def dist_validation(self, dataloader, current_iter, tb_logger, save_img):
        """Distributed validation."""
        if self.opt['rank'] == 0:
            self.nondist_validation(dataloader, current_iter, tb_logger, save_img)

    def nondist_validation(self, dataloader, current_iter, tb_logger, save_img):
        """Non-distributed validation."""
        dataset_name = dataloader.dataset.opt['name']
        with_metrics = self.opt['val'].get('metrics') is not None
        use_pbar = self.opt['val'].get('pbar', False)

        if with_metrics:
            if not hasattr(self, 'metric_results'):  # only execute in the first run
                self.metric_results = {metric: 0 for metric in self.opt['val']['metrics'].keys()}
            # initialize the best metric results for each dataset_name (supporting multiple validation datasets)
            self._initialize_best_metric_results(dataset_name)
        # zero self.metric_results
        if with_metrics:
            self.metric_results = {metric: 0 for metric in self.metric_results}

        metric_data = dict()
        if use_pbar:
            pbar = tqdm(total=len(dataloader), unit='image')

        for idx, val_data in enumerate(dataloader):
            img_name = osp.splitext(osp.basename(val_data['lq_path'][0]))[0]
            self.feed_data(val_data)
            self.test()

            visuals = self.get_current_visuals()
            sr_img = tensor2img([visuals['result']])
            metric_data['img'] = sr_img
            if 'gt' in visuals:
                gt_img = tensor2img([visuals['gt']])
                metric_data['img2'] = gt_img
                del self.gt_rgb

            # tentative for out of GPU memory
            del self.lq_rgb
            del self.output
            torch.cuda.empty_cache()

            if save_img:
                if self.opt['is_train']:
                    save_img_path = osp.join(self.opt['path']['visualization'], img_name,
                                           f'{img_name}_{current_iter}.png')
                else:
                    if self.opt['val']['suffix']:
                        save_img_path = osp.join(self.opt['path']['visualization'], dataset_name,
                                               f'{img_name}_{self.opt["val"]["suffix"]}.png')
                    else:
                        save_img_path = osp.join(self.opt['path']['visualization'], dataset_name,
                                               f'{img_name}_{self.opt["name"]}.png')
                imwrite(sr_img, save_img_path)

            if with_metrics:
                # calculate metrics
                for name, opt_ in self.opt['val']['metrics'].items():
                    self.metric_results[name] += calculate_metric(metric_data, opt_)
            if use_pbar:
                pbar.update(1)
                pbar.set_description(f'Test {img_name}')

        if use_pbar:
            pbar.close()

        if with_metrics:
            for metric in self.metric_results.keys():
                self.metric_results[metric] /= (idx + 1)
                # update the best metric result
                self._update_best_metric_result(dataset_name, metric, self.metric_results[metric], current_iter)

            self._log_validation_metric_values(current_iter, dataset_name, tb_logger)

    def get_current_visuals(self):
        """Get current visual results."""
        out_dict = OrderedDict()
        out_dict['lq'] = self.lq_rgb.detach().cpu()
        out_dict['result'] = self.output.detach().cpu()
        if hasattr(self, 'gt_rgb'):
            out_dict['gt'] = self.gt_rgb.detach().cpu()
        return out_dict

    def save(self, epoch, current_iter):
        """Save networks and training state."""
        self.save_network([self.net_g, self.net_le, self.net_flow], 'net_g', current_iter)
        if self.net_flow_condition:
            self.save_network(self.net_flow_condition, 'net_flow_condition', current_iter)
        self.save_training_state(epoch, current_iter)
