import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import multivariate_normal
from scipy import stats
import seaborn as sns

def sigma_y(t, beta=10):
    """
    Compute the interpolation weight for auxiliary variable y.
    
    Args:
        t: Time parameter in [0, 1]
        beta: Sharpness parameter controlling the transition
    
    Returns:
        Weight for y interpolation
    """
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """
    Rectified flow interpolation between two states.
    
    Args:
        t: Time parameter in [0, 1]
        x0, x1: Initial and final states for main variable x
        y0, y1: Initial and final states for auxiliary variable y
        beta: Sharpness parameter for y interpolation
    
    Returns:
        Concatenated interpolated state [x_t, y_t]
    """
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

def entropy_gaussian(cov):
    """
    Compute entropy of multivariate Gaussian distribution.
    
    Args:
        cov: Covariance matrix
    
    Returns:
        Differential entropy in nats
    """
    k = cov.shape[0]
    det_cov = np.linalg.det(cov)
    if det_cov <= 0:
        return float('inf')  # Handle degenerate case
    return 0.5 * k * (1 + np.log(2 * np.pi)) + 0.5 * np.log(det_cov)

def compute_velocity_field(t, x0, x1, y0, y1, beta=10):
    """
    Compute the velocity field (time derivative) of the rectified flow.
    
    Args:
        t: Time parameter
        x0, x1: Initial and final states for x
        y0, y1: Initial and final states for y
        beta: Sharpness parameter
    
    Returns:
        Velocity field [v_x, v_y]
    """
    # Velocity for x (constant)
    v_x = x1 - x0
    
    # Velocity for y (time-dependent)
    sigma_y_prime = beta / (1 - t + beta)**2  # Derivative of sigma_y
    v_y = (y1 - y0) * sigma_y_prime
    
    return v_x, v_y

def run_single_experiment(x0, x1, y0, y1, beta, ts, experiment_id=0):
    """
    Run a single rectified flow experiment.
    
    Args:
        x0, x1: Initial and final states for main variable
        y0, y1: Initial and final states for auxiliary variable  
        beta: Sharpness parameter
        ts: Time steps
        experiment_id: Experiment identifier
    
    Returns:
        Dictionary containing experiment results
    """
    # Compute interpolated states
    states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
    
    # Compute velocity fields
    velocities_x = []
    velocities_y = []
    for t in ts:
        v_x, v_y = compute_velocity_field(t, x0, x1, y0, y1, beta)
        velocities_x.append(v_x)
        velocities_y.append(v_y)
    
    velocities_x = np.array(velocities_x)
    velocities_y = np.array(velocities_y)
    
    # Compute entropy evolution
    entropies = []
    sigma_y_values = []
    for t in ts:
        sigma_y_t = sigma_y(t, beta)
        sigma_y_values.append(sigma_y_t)
        
        # Covariance matrices
        cov_y = np.eye(3) * (sigma_y_t * 0.1)**2
        cov_x = np.eye(3) * (0.1 + t * 0.1)**2
        
        ent = entropy_gaussian(cov_x) + entropy_gaussian(cov_y)
        entropies.append(ent)
    
    # Compute additional statistics
    x_norms = np.linalg.norm(states[:, :3], axis=1)
    y_norms = np.linalg.norm(states[:, 3:], axis=1)
    
    # Compute flow metrics
    total_distance_x = np.sum(np.linalg.norm(np.diff(states[:, :3], axis=0), axis=1))
    total_distance_y = np.sum(np.linalg.norm(np.diff(states[:, 3:], axis=0), axis=1))
    
    return {
        'experiment_id': experiment_id,
        'states': states,
        'velocities_x': velocities_x,
        'velocities_y': velocities_y,
        'entropies': entropies,
        'sigma_y_values': sigma_y_values,
        'x_norms': x_norms,
        'y_norms': y_norms,
        'total_distance_x': total_distance_x,
        'total_distance_y': total_distance_y,
        'final_entropy': entropies[-1],
        'entropy_change': entropies[-1] - entropies[0],
        'max_velocity_x': np.max(np.linalg.norm(velocities_x, axis=1)),
        'max_velocity_y': np.max(np.linalg.norm(velocities_y, axis=1))
    }

def compute_confidence_intervals(data, confidence=0.95):
    """
    Compute confidence intervals for data.
    
    Args:
        data: Array of data points
        confidence: Confidence level (default 0.95)
    
    Returns:
        Dictionary with mean, std, and confidence intervals
    """
    data = np.array(data)
    mean = np.mean(data, axis=0)
    std = np.std(data, axis=0)
    n = len(data)
    
    # Compute confidence intervals
    alpha = 1 - confidence
    t_critical = stats.t.ppf(1 - alpha/2, n-1)
    margin_error = t_critical * std / np.sqrt(n)
    
    return {
        'mean': mean,
        'std': std,
        'lower_ci': mean - margin_error,
        'upper_ci': mean + margin_error,
        'n_samples': n
    }

# Statistical Analysis of Rectified Flow
print("="*80)
print("STATISTICAL ANALYSIS OF RECTIFIED FLOW")
print("="*80)

# Experimental parameters
n_experiments = 50  # Number of statistical experiments
beta_values = [5, 10, 20]  # Different sharpness parameters to test
n_time_steps = 100
ts = np.linspace(0, 1, n_time_steps)

# Fixed initial HQ state
x0_base = np.array([5.0, 5.0, 5.0])
y0_base = np.zeros(3)

# Fixed final LQ state (base)
x1_base = np.array([1.0, 1.0, 1.0])

print(f"Running {n_experiments} experiments for each beta value: {beta_values}")
print(f"Time steps: {n_time_steps}")
print(f"Base HQ state: x0={x0_base}, y0={y0_base}")
print(f"Base LQ state: x1={x1_base}")

# Storage for all experiments
all_experiments = []
experiment_counter = 0

# Run experiments for different beta values
for beta in beta_values:
    print(f"\n--- Running experiments for β = {beta} ---")
    
    beta_experiments = []
    
    for exp_id in range(n_experiments):
        # Add some randomness to initial and final states for statistical variation
        np.random.seed(exp_id + beta * 1000)  # Reproducible but varied
        
        # Slightly perturb the states
        x0 = x0_base + np.random.normal(0, 0.1, 3)
        y0 = y0_base + np.random.normal(0, 0.05, 3)
        x1 = x1_base + np.random.normal(0, 0.1, 3)
        y1 = np.random.randn(3) * 0.5  # Random auxiliary final state
        
        # Run single experiment
        result = run_single_experiment(x0, x1, y0, y1, beta, ts, experiment_counter)
        result['beta'] = beta
        
        beta_experiments.append(result)
        all_experiments.append(result)
        experiment_counter += 1
        
        if (exp_id + 1) % 10 == 0:
            print(f"  Completed {exp_id + 1}/{n_experiments} experiments")
    
    # Compute statistics for this beta
    entropies_final = [exp['final_entropy'] for exp in beta_experiments]
    entropy_changes = [exp['entropy_change'] for exp in beta_experiments]
    max_vel_x = [exp['max_velocity_x'] for exp in beta_experiments]
    max_vel_y = [exp['max_velocity_y'] for exp in beta_experiments]
    
    print(f"  Final entropy: {np.mean(entropies_final):.3f} ± {np.std(entropies_final):.3f}")
    print(f"  Entropy change: {np.mean(entropy_changes):.3f} ± {np.std(entropy_changes):.3f}")
    print(f"  Max velocity x: {np.mean(max_vel_x):.3f} ± {np.std(max_vel_x):.3f}")
    print(f"  Max velocity y: {np.mean(max_vel_y):.3f} ± {np.std(max_vel_y):.3f}")

print(f"\nTotal experiments completed: {len(all_experiments)}")

# Organize data by beta for statistical analysis
beta_5_data = [exp for exp in all_experiments if exp['beta'] == 5]
beta_10_data = [exp for exp in all_experiments if exp['beta'] == 10]
beta_20_data = [exp for exp in all_experiments if exp['beta'] == 20]

# Extract key metrics for statistical analysis
metrics = ['final_entropy', 'entropy_change', 'max_velocity_x', 'max_velocity_y',
           'total_distance_x', 'total_distance_y']

# Compute confidence intervals for each metric and beta
print("\n" + "="*80)
print("STATISTICAL CONFIDENCE INTERVALS (95%)")
print("="*80)

for metric in metrics:
    print(f"\n{metric.upper().replace('_', ' ')}:")
    for beta in beta_values:
        beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
        values = [exp[metric] for exp in beta_data]
        ci = compute_confidence_intervals(values)

        print(f"  β={beta:2d}: {ci['mean']:.4f} ± {ci['std']:.4f} "
              f"[{ci['lower_ci']:.4f}, {ci['upper_ci']:.4f}] (n={ci['n_samples']})")

# Statistical significance tests (ANOVA)
print("\n" + "="*80)
print("STATISTICAL SIGNIFICANCE TESTS (ANOVA)")
print("="*80)

for metric in metrics:
    beta_5_values = [exp[metric] for exp in beta_5_data]
    beta_10_values = [exp[metric] for exp in beta_10_data]
    beta_20_values = [exp[metric] for exp in beta_20_data]

    # Perform one-way ANOVA
    f_stat, p_value = stats.f_oneway(beta_5_values, beta_10_values, beta_20_values)

    significance = "***" if p_value < 0.001 else "**" if p_value < 0.01 else "*" if p_value < 0.05 else "ns"

    print(f"{metric.replace('_', ' ').title():20s}: F={f_stat:8.3f}, p={p_value:.6f} {significance}")

print("\nSignificance levels: *** p<0.001, ** p<0.01, * p<0.05, ns = not significant")

# Create comprehensive statistical visualization
plt.style.use('seaborn-v0_8')
fig = plt.figure(figsize=(24, 16))

# Color palette for different betas
colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
beta_labels = [f'β = {b}' for b in beta_values]

# Plot 1: Distribution of final entropies
plt.subplot(4, 4, 1)
for i, beta in enumerate(beta_values):
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    values = [exp['final_entropy'] for exp in beta_data]
    plt.hist(values, alpha=0.7, label=beta_labels[i], color=colors[i], bins=15)
plt.title('Distribution of Final Entropies', fontweight='bold')
plt.xlabel('Final Entropy (nats)')
plt.ylabel('Frequency')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 2: Box plot of entropy changes
plt.subplot(4, 4, 2)
entropy_changes_by_beta = []
for beta in beta_values:
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    values = [exp['entropy_change'] for exp in beta_data]
    entropy_changes_by_beta.append(values)

bp = plt.boxplot(entropy_changes_by_beta, labels=beta_labels, patch_artist=True)
for patch, color in zip(bp['boxes'], colors):
    patch.set_facecolor(color)
    patch.set_alpha(0.7)
plt.title('Entropy Change Distribution', fontweight='bold')
plt.ylabel('Entropy Change (nats)')
plt.grid(True, alpha=0.3)

# Plot 3: Velocity magnitude comparison
plt.subplot(4, 4, 3)
for i, beta in enumerate(beta_values):
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    vel_x = [exp['max_velocity_x'] for exp in beta_data]
    vel_y = [exp['max_velocity_y'] for exp in beta_data]
    plt.scatter(vel_x, vel_y, alpha=0.6, label=beta_labels[i], color=colors[i], s=30)
plt.title('Max Velocity Comparison', fontweight='bold')
plt.xlabel('Max Velocity X')
plt.ylabel('Max Velocity Y')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 4: Path length analysis
plt.subplot(4, 4, 4)
for i, beta in enumerate(beta_values):
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    dist_x = [exp['total_distance_x'] for exp in beta_data]
    dist_y = [exp['total_distance_y'] for exp in beta_data]
    plt.scatter(dist_x, dist_y, alpha=0.6, label=beta_labels[i], color=colors[i], s=30)
plt.title('Total Path Distances', fontweight='bold')
plt.xlabel('Total Distance X')
plt.ylabel('Total Distance Y')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 5-8: Time series with confidence intervals
time_series_metrics = ['entropies', 'x_norms', 'y_norms', 'sigma_y_values']
time_series_titles = ['Entropy Evolution', 'X-Norm Evolution', 'Y-Norm Evolution', 'σ_y Evolution']

for plot_idx, (metric, title) in enumerate(zip(time_series_metrics, time_series_titles)):
    plt.subplot(4, 4, 5 + plot_idx)

    for i, beta in enumerate(beta_values):
        beta_data = [exp for exp in all_experiments if exp['beta'] == beta]

        # Collect time series data
        time_series_data = []
        for exp in beta_data:
            time_series_data.append(exp[metric])

        time_series_data = np.array(time_series_data)

        # Compute mean and confidence intervals
        mean_series = np.mean(time_series_data, axis=0)
        std_series = np.std(time_series_data, axis=0)
        n = len(time_series_data)

        # 95% confidence interval
        ci_margin = 1.96 * std_series / np.sqrt(n)

        # Plot mean line
        plt.plot(ts, mean_series, color=colors[i], linewidth=2, label=beta_labels[i])

        # Plot confidence interval
        plt.fill_between(ts, mean_series - ci_margin, mean_series + ci_margin,
                        color=colors[i], alpha=0.2)

    plt.title(f'{title} (Mean ± 95% CI)', fontweight='bold')
    plt.xlabel('Time t')
    plt.ylabel(title.split()[0])
    if plot_idx == 0:  # Only show legend for first plot
        plt.legend()
    plt.grid(True, alpha=0.3)

# Plot 9: Correlation matrix heatmap
plt.subplot(4, 4, 9)
correlation_metrics = ['final_entropy', 'entropy_change', 'max_velocity_x', 'max_velocity_y',
                      'total_distance_x', 'total_distance_y']
correlation_data = []
for exp in all_experiments:
    row = [exp[metric] for metric in correlation_metrics]
    correlation_data.append(row)

correlation_matrix = np.corrcoef(np.array(correlation_data).T)
im = plt.imshow(correlation_matrix, cmap='RdBu_r', vmin=-1, vmax=1)
plt.colorbar(im, shrink=0.8)
plt.title('Metric Correlations', fontweight='bold')
plt.xticks(range(len(correlation_metrics)), [m.replace('_', '\n') for m in correlation_metrics], rotation=45)
plt.yticks(range(len(correlation_metrics)), [m.replace('_', '\n') for m in correlation_metrics])

# Add correlation values to heatmap
for i in range(len(correlation_metrics)):
    for j in range(len(correlation_metrics)):
        plt.text(j, i, f'{correlation_matrix[i, j]:.2f}',
                ha='center', va='center', fontsize=8)

# Plot 10: Beta effect on key metrics
plt.subplot(4, 4, 10)
beta_means = []
beta_stds = []
for beta in beta_values:
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    final_entropies = [exp['final_entropy'] for exp in beta_data]
    beta_means.append(np.mean(final_entropies))
    beta_stds.append(np.std(final_entropies))

plt.errorbar(beta_values, beta_means, yerr=beta_stds, marker='o', linewidth=2,
            markersize=8, capsize=5, capthick=2)
plt.title('Beta Effect on Final Entropy', fontweight='bold')
plt.xlabel('Beta Parameter')
plt.ylabel('Final Entropy (nats)')
plt.grid(True, alpha=0.3)

# Plot 11: Distribution comparison (violin plot)
plt.subplot(4, 4, 11)
velocity_data_by_beta = []
for beta in beta_values:
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    velocities = [exp['max_velocity_y'] for exp in beta_data]
    velocity_data_by_beta.append(velocities)

parts = plt.violinplot(velocity_data_by_beta, positions=beta_values, widths=2, showmeans=True)
for pc, color in zip(parts['bodies'], colors):
    pc.set_facecolor(color)
    pc.set_alpha(0.7)
plt.title('Max Y-Velocity Distribution', fontweight='bold')
plt.xlabel('Beta Parameter')
plt.ylabel('Max Velocity Y')
plt.grid(True, alpha=0.3)

# Plot 12: Trajectory variance analysis
plt.subplot(4, 4, 12)
for i, beta in enumerate(beta_values):
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]

    # Compute trajectory variance at different time points
    trajectory_vars = []
    for t_idx in range(0, len(ts), 10):  # Sample every 10th time point
        x_positions = [exp['states'][t_idx, 0] for exp in beta_data]  # x1 component
        trajectory_vars.append(np.var(x_positions))

    sample_times = ts[::10]
    plt.plot(sample_times, trajectory_vars, marker='o', linewidth=2,
            label=beta_labels[i], color=colors[i])

plt.title('Trajectory Variance Over Time', fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('Variance in x₁')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('rectified_flow_statistical_analysis.png', dpi=300, bbox_inches='tight')
plt.show()

# Print detailed statistical summary
print("\n" + "="*80)
print("DETAILED STATISTICAL SUMMARY")
print("="*80)

for beta in beta_values:
    beta_data = [exp for exp in all_experiments if exp['beta'] == beta]
    print(f"\nBETA = {beta}:")
    print("-" * 40)

    # Summary statistics
    final_entropies = [exp['final_entropy'] for exp in beta_data]
    entropy_changes = [exp['entropy_change'] for exp in beta_data]
    max_vel_x = [exp['max_velocity_x'] for exp in beta_data]
    max_vel_y = [exp['max_velocity_y'] for exp in beta_data]

    print(f"Final Entropy    : μ={np.mean(final_entropies):.4f}, σ={np.std(final_entropies):.4f}")
    print(f"Entropy Change   : μ={np.mean(entropy_changes):.4f}, σ={np.std(entropy_changes):.4f}")
    print(f"Max Velocity X   : μ={np.mean(max_vel_x):.4f}, σ={np.std(max_vel_x):.4f}")
    print(f"Max Velocity Y   : μ={np.mean(max_vel_y):.4f}, σ={np.std(max_vel_y):.4f}")

    # Percentiles
    print(f"Final Entropy Percentiles: 25%={np.percentile(final_entropies, 25):.4f}, "
          f"50%={np.percentile(final_entropies, 50):.4f}, 75%={np.percentile(final_entropies, 75):.4f}")

print("\n" + "="*80)
print("ANALYSIS COMPLETE - Statistical significance and distributions analyzed")
print("="*80)
