# PolarFree Flow Matching Test Configuration

name: test_flow_matching
model_type: PolarFree_FlowMatching
scale: 1
num_gpu: 1  # set num_gpu: 0 for cpu mode
manual_seed: 100

datasets:
  test_1:
    name: TestSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: ['00', '01', '02', '03', '04', '05', '06', '07']

# network structures (same as training)
network_g:
  type: Transformer
  inp_channels: 3
  out_channels: 3
  dim: 48
  num_blocks: [3,4,4,4]
  num_refinement_blocks: 4
  heads: [1,2,4,8]
  ffn_expansion_factor: 2.66
  bias: False
  LayerNorm_type: WithBias
  dual_pixel_task: False
  embed_dim: 64
  group: 4

network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 64
  block_num: 6
  group: 4
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

network_flow:
  type: PolarFlowMatching
  in_channels: 64
  out_channels: 64
  condition_dim: 64
  model_channels: 128
  num_res_blocks: 2
  channel_mult: [1, 2, 4, 8]
  attention_resolutions: [16, 8]
  num_heads: 8
  dropout: 0.0  # no dropout during inference
  sigma: 0.0
  use_ot: false

network_flow_condition:
  type: latent_encoder_gelu
  in_chans: 17  # RGB(3) + img0(3) + img45(3) + img90(3) + img135(3) + aolp(1) + dolp(1) = 17 channels
  embed_dim: 64
  block_num: 4
  group: 4
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 2

# Flow matching configuration for inference
flow_matching:
  num_steps: 50  # can be reduced for faster inference
  solver: euler  # euler is fastest, rk4 for better quality
  sigma: 0.0
  use_conditioning: true

# path to trained models
path:
  pretrain_network_g: experiments/train_flow_matching/models/net_g_latest.pth
  param_key_g: params
  strict_load_g: true

# validation settings
val:
  save_img: true
  suffix: flow_matching  # suffix for output images

  metrics:
    psnr:
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false
    ssim:
      type: calculate_ssim
      crop_border: 0
      test_y_channel: false
    lpips:
      type: calculate_lpips
      better: lower
