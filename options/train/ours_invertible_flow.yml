# 可逆流匹配训练配置
# Invertible Flow Matching Training Configuration

name: PolarFree_InvertibleFlow_x4
model_type: PolarFree_InvertibleFlow
scale: 4
num_gpu: 1
manual_seed: 0

# 数据集配置
datasets:
  train:
    name: PolarTrain
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: [ ]

    easy_data_ratio: 1
    hard_data_ratio: 3

    gt_size: 256
    use_hflip: true
    use_rot: false

    # 数据加载器
    use_shuffle: true
    num_worker_per_gpu: 6
    batch_size_per_gpu: 4
    dataset_enlarge_ratio: 1
    prefetch_mode: ~

  val:
    name: PolarVal
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: [ ]

# 网络结构
network_g:
  type: Transformer
  inp_channels: 3
  out_channels: 3
  dim: 48
  num_blocks: [3,4,4,4]
  num_refinement_blocks: 4
  heads: [1,2,4,8]
  ffn_expansion_factor: 2.66
  bias: False
  LayerNorm_type: WithBias
  dual_pixel_task: False
  embed_dim: 64
  group: 4

network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 64
  block_num: 6
  group: 4
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

# 可逆流匹配网络
network_inv_flow:
  type: InvertibleFlowMatching
  in_channels: 64  # embed_dim from latent encoder
  out_channels: 64
  model_channels: 128
  num_blocks: 4
  condition_dim: 64
  subnet_type: 'Resnet'
  time_embed_dim: 512
  use_haar_downsampling: true
  sigma: 0.0
  clamp: 1.0

# 流条件网络
network_flow_condition:
  type: latent_encoder_gelu
  in_chans: 17  # RGB(3) + img0(3) + img45(3) + img90(3) + img135(3) + aolp(1) + dolp(1) = 17 channels
  embed_dim: 64
  block_num: 4
  group: 4
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 2

# 可逆流匹配设置
invertible_flow:
  num_steps: 50
  solver: euler
  sigma: 0.0
  loss_weight: 1.0
  recon_loss_weight: 0.1
  use_conditioning: true

# 路径配置
path:
  pretrain_network_g: experiments/pretrained_models/polarfree_transformer.pth
  pretrain_network_le: experiments/pretrained_models/polarfree_latent_encoder.pth
  strict_load_g: false
  strict_load_le: false
  resume_state: ~

# 训练设置
train:
  ema_decay: 0.999
  optim_g:
    type: Adam
    lr: !!float 2e-4
    weight_decay: 0
    betas: [0.9, 0.99]

  scheduler:
    type: MultiStepLR
    milestones: [250000, 400000, 450000, 475000]
    gamma: 0.5

  total_iter: 500000
  warmup_iter: -1  # no warm up

  # 损失函数
  pixel_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

  perceptual_opt:
    type: PerceptualLoss
    layer_weights:
      'conv5_4': 1  # before relu
    vgg_type: vgg19
    use_input_norm: true
    range_norm: false
    perceptual_weight: 1.0
    style_weight: 0
    criterion: l1

# 验证设置
val:
  val_freq: !!float 5e3
  save_img: false

  metrics:
    psnr: # metric name
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false

# 日志设置
logger:
  print_freq: 100
  save_checkpoint_freq: !!float 5e3
  use_tb_logger: true
  wandb:
    project: ~
    resume_id: ~

# 分布式训练设置
dist_params:
  backend: nccl
  port: 29500
