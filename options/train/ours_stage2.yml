# general settings
name:  train_stage2
model_type: PolarFree_S2
scale: 1
num_gpu: auto  # set num_gpu: 0 for cpu mode
manual_seed: 100

# dataset and data loader settings
datasets:
  train:
    name: TrainSet
  train:
    name: TrainSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: [ ]

    easy_data_ratio: 1
    hard_data_ratio: 3

    # data loader
    use_shuffle: true
    num_worker_per_gpu: 6
    batch_size_per_gpu: 2
    dataset_enlarge_ratio: 1
    prefetch_mode: ~

    gt_size: 256   

    dataset_enlarge_ratio: 1
    prefetch_mode: ~

  val:
    name: ValSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: ['00', '01', '02', '03', '04', '05', '06', '07']

# network structures
network_g:
  type: Transformer
  inp_channels: 3
  out_channels: 3
  dim: 48
  num_blocks: [3,4,4,4]
  num_refinement_blocks: 4
  heads: [1,2,4,8]
  ffn_expansion_factor: 2.66
  bias: False
  LayerNorm_type: WithBias
  dual_pixel_task: False
  embed_dim: 64
  group: 4 # N=4*4



network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 64 # same as above
  block_num: 6
  group: 4 # same as above
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

network_le_dm:
  type: latent_encoder_gelu
  in_chans: 9
  embed_dim: 64 # same as above
  block_num: 6
  group: 4 # same as above
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 4


network_d:
  type: denoising
  in_channel: 256 # (embed_dim*4)
  out_channel: 256 # (embed_dim*4)
  inner_channel: 512
  block_num: 4
  group: 4 # same as above
  patch_expansion: 0.5
  channel_expansion: 2

diffusion_schedule:
  apply_ldm: False
  schedule: linear
  timesteps: 8
  linear_start: 0.1 # 1e-6
  linear_end: 0.99 # 1e-2

# path
path:
  pretrain_network_g: experiments/train_stage1/models/net_g_45000.pth
  param_key_g: params
  strict_load_g: true

  pretrain_network_le: experiments/train_stage1/models/net_le_45000.pth
  param_key_g: params
  strict_load_le: true

  pretrain_network_le_dm: ~
  param_key_g: params
  strict_load_le_dm: true

  pretrain_network_d: ~
  param_key_g: params
  strict_load_d: true

  resume_state: ~

# training settings
train:
  total_iter: 300000
  warmup_iter: -1 # no warm up
  use_grad_clip: True

  scheduler:
    type: CosineAnnealingRestartCyclicLR
    periods: [100000, 200000]       
    restart_weights: [1,1]
    eta_mins: [0.0002,0.000001]
  
  mixing_augs:
    mixup: false
    mixup_beta: 1.2
    use_identity: true

  optim_total:
    type: AdamW
    lr: !!float 2e-4
    weight_decay: !!float 1e-4
    betas: [0.9, 0.999]
  
  # losses
  pixel_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

  tv_opt:
    loss_weight: 0.0005

  vgg_opt:
    loss_weight: 0.02
  pixel_diff_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

  phase_opt:
    loss_weight: 0.1

# validation settings
val:
  val_freq: !!float 100000
  save_img: true

  metrics:
    psnr: # metric name, can be arbitrary
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false

# logging settings
logger:
  print_freq: 10000
  save_checkpoint_freq: !!float 100000
  use_tb_logger: false
  wandb:
    project: ~
    resume_id: ~

# dist training settings
dist_params:
  backend: nccl
  port: 29500
