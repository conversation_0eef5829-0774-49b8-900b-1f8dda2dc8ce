# PolarFree Flow Matching Training Configuration
# Integrates modern flow matching with existing PolarFree architecture

# general settings
name: train_flow_matching
model_type: PolarFree_FlowMatching
scale: 1
num_gpu: auto  # set num_gpu: 0 for cpu mode
manual_seed: 100

# dataset and data loader settings
datasets:
  train:
    name: TrainSet
    type: PairedImagePolarDataset
    phase: train
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: [ ]

    easy_data_ratio: 1
    hard_data_ratio: 3

    # data loader
    use_shuffle: true
    num_worker_per_gpu: 6
    batch_size_per_gpu: 2
    dataset_enlarge_ratio: 1
    prefetch_mode: ~

    gt_size: 256

  val:
    name: ValSet
    type: PairedImagePolarDataset
    phase: val
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: ['00', '01', '02', '03', '04', '05', '06', '07']

# network structures
network_g:
  type: Transformer
  inp_channels: 3
  out_channels: 3
  dim: 48
  num_blocks: [3,4,4,4]
  num_refinement_blocks: 4
  heads: [1,2,4,8]
  ffn_expansion_factor: 2.66
  bias: False
  LayerNorm_type: WithBias
  dual_pixel_task: False
  embed_dim: 64
  group: 4 # N=4*4

network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 64 # same as above
  block_num: 6
  group: 4 # same as above
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

# Flow matching network
network_flow:
  type: PolarFlowMatching
  in_channels: 64  # embed_dim from latent encoder
  out_channels: 64
  condition_dim: 64
  model_channels: 128
  num_res_blocks: 2
  channel_mult: [1, 2, 4, 8]
  attention_resolutions: [16, 8]
  num_heads: 8
  dropout: 0.1
  sigma: 0.0  # noise level for flow matching
  use_ot: false  # optimal transport

# Optional: Flow conditioning network
network_flow_condition:
  type: latent_encoder_gelu
  in_chans: 17  # RGB(3) + img0(3) + img45(3) + img90(3) + img135(3) + aolp(1) + dolp(1) = 17 channels
  embed_dim: 64
  block_num: 4
  group: 4
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 2

# Flow matching configuration
flow_matching:
  num_steps: 50  # number of integration steps during inference
  solver: euler  # ODE solver: euler, rk4, dopri5
  sigma: 0.0  # noise level
  loss_weight: 1.0  # weight for flow matching loss
  use_conditioning: true  # use conditioning network

# path
path:
  # Load pretrained Stage 1 models (set to ~ if not available)
  pretrain_network_g: ~  # experiments/train_stage1/models/net_g_45000.pth
  param_key_g: params
  strict_load_g: true

  pretrain_network_le: ~  # experiments/train_stage1/models/net_le_45000.pth
  param_key_le: params
  strict_load_le: true

  # Pretrained flow matching model (optional)
  # Options:
  # 1. Local path: path/to/model.pth
  # 2. Pretrained model: pretrained:stable-diffusion-v1-5
  # 3. URL: https://example.com/model.pth
  # 4. Hugging Face: username/repo-name
  pretrain_network_flow: ~  # pretrained:stable-diffusion-v1-5
  param_key_flow: params
  strict_load_flow: false  # allow partial loading

  pretrain_network_flow_condition: ~
  param_key_flow_condition: params
  strict_load_flow_condition: true

  resume_state: ~

# Cache directory for pretrained models
cache_dir: ~/.cache/polarfree

# training settings
train:
  total_iter: 300000
  warmup_iter: -1 # no warm up
  use_grad_clip: True
  
  # Whether to freeze Stage 1 networks
  freeze_stage1: false

  scheduler:
    type: MultiStepLR
    milestones: [100000, 200000]
    gamma: 0.5
  
  mixing_augs:
    mixup: false
    mixup_beta: 1.2
    use_identity: true

  optim_g:
    type: Adam
    lr: !!float 2e-4
    weight_decay: !!float 1e-4
    betas: [0.9, 0.999]
  
  # losses
  pixel_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

  # Flow matching loss
  flow_opt:
    type: MSELoss
    loss_weight: 1.0
    reduction: mean

  # Perceptual loss (optional) - disabled due to network issues
  # perceptual_opt:
  #   type: PerceptualLoss
  #   layer_weights:
  #     'conv5_4': 1  # before relu
  #   vgg_type: vgg19
  #   use_input_norm: true
  #   range_norm: false
  #   perceptual_weight: 1.0
  #   style_weight: 0
  #   criterion: l1
  #   loss_weight: 0.1

  tv_opt:
    loss_weight: 0.0005

  vgg_opt:
    loss_weight: 0.02

# validation settings
val:
  val_freq: !!float 5000
  save_img: true

  metrics:
    psnr: # metric name, can be arbitrary
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false
    ssim:
      type: calculate_ssim
      crop_border: 0
      test_y_channel: false

# logging settings
logger:
  print_freq: 1000
  save_checkpoint_freq: !!float 10000
  use_tb_logger: false
  wandb:
    project: ~
    resume_id: ~

# dist training settings
dist_params:
  backend: nccl
  port: 29500
