# Simple Flow Matching training configuration
name: train_flow_matching_simple
model_type: PolarFree_FlowMatching
scale: 1
num_gpu: 1
manual_seed: 100

# datasets
datasets:
  train:
    name: TrainSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: []
    easy_data_ratio: 1
    hard_data_ratio: 3
    use_shuffle: true
    num_worker_per_gpu: 2
    batch_size_per_gpu: 1
    dataset_enlarge_ratio: 1
    prefetch_mode: ~
    gt_size: 128
    phase: train
    scale: 1

  val:
    name: ValSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/test
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: ['00', '01', '02', '03', '04', '05', '06', '07']
    phase: val
    scale: 1

# network structures
network_g:
  type: Transformer
  inp_channels: 3
  out_channels: 3
  dim: 24
  num_blocks: [1, 1, 1, 1]
  num_refinement_blocks: 1
  heads: [1, 2, 4, 8]
  ffn_expansion_factor: 2.66
  bias: False
  LayerNorm_type: WithBias
  dual_pixel_task: False
  embed_dim: 32
  group: 4

network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 32
  block_num: 2
  group: 4
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

network_flow:
  type: PolarFlowMatching
  in_channels: 32
  out_channels: 32
  condition_dim: 32
  model_channels: 64
  num_res_blocks: 1
  channel_mult: [1, 2, 4]
  attention_resolutions: [16, 8]
  num_heads: 4
  dropout: 0.1
  sigma: 0.0
  use_ot: false

network_flow_condition:
  type: latent_encoder_gelu
  in_chans: 17  # RGB(3) + img0(3) + img45(3) + img90(3) + img135(3) + aolp(1) + dolp(1) = 17 channels
  embed_dim: 32
  block_num: 2
  group: 4
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 2

# Flow matching settings
flow_matching:
  num_steps: 10
  solver: euler
  sigma: 0.0
  loss_weight: 1.0
  use_conditioning: true

# path
path:
  pretrain_network_g: ~
  param_key_g: params
  strict_load_g: true
  pretrain_network_le: ~
  param_key_le: params
  strict_load_le: true
  pretrain_network_flow: ~
  param_key_flow: params
  strict_load_flow: false
  pretrain_network_flow_condition: ~
  param_key_flow_condition: params
  strict_load_flow_condition: true
  resume_state: ~

# training settings
train:
  total_iter: 1000
  warmup_iter: -1
  use_grad_clip: true
  freeze_stage1: false

  scheduler:
    type: MultiStepLR
    milestones: [500]
    gamma: 0.5

  mixing_augs:
    mixup: false
    mixup_beta: 1.2
    use_identity: true

  optim_g:
    type: Adam
    lr: !!float 1e-4
    weight_decay: 0
    betas: [0.9, 0.999]

  # losses
  pixel_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

  flow_opt:
    type: MSELoss
    loss_weight: 1.0
    reduction: mean

# validation settings
val:
  val_freq: !!float 500
  save_img: true

  metrics:
    psnr: # metric name, can be arbitrary
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false
    ssim:
      type: calculate_ssim
      crop_border: 0
      test_y_channel: false

# logging settings
logger:
  print_freq: 100
  save_checkpoint_freq: !!float 500
  use_tb_logger: false
  wandb:
    project: ~
    resume_id: ~

# dist training settings
dist_params:
  backend: nccl
  port: 29500
