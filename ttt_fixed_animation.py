import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation between two states."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

# Animation setup
print("="*60)
print("RECTIFIED FLOW TRAJECTORY ANIMATION - FIXED VERSION")
print("="*60)

# Parameters
n_trajectories = 9
beta_values = [5, 10, 20]
n_time_steps = 150
ts = np.linspace(0, 1, n_time_steps)

# Generate trajectories
trajectories = []
colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#FFD93D', '#6BCF7F', '#A8E6CF', '#FF8B94', '#B4A7D6', '#D4A574']

print(f"Generating trajectories for β = {beta_values}")

traj_counter = 0
for beta in beta_values:
    for i in range(3):  # 3 trajectories per beta
        np.random.seed(traj_counter + beta * 10)
        
        # Create varied initial and final states
        x0 = np.array([4.5 + np.random.uniform(-0.5, 0.5), 
                      4.5 + np.random.uniform(-0.5, 0.5), 
                      4.5 + np.random.uniform(-0.5, 0.5)])
        y0 = np.random.normal(0, 0.1, 3)
        
        x1 = np.array([1.0 + np.random.uniform(-0.3, 0.3), 
                      1.0 + np.random.uniform(-0.3, 0.3), 
                      1.0 + np.random.uniform(-0.3, 0.3)])
        y1 = np.random.normal(0, 0.8, 3)
        
        # Compute trajectory
        states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
        
        trajectories.append({
            'beta': beta,
            'states': states,
            'color': colors[traj_counter % len(colors)],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1,
            'id': traj_counter
        })
        traj_counter += 1

print(f"Generated {len(trajectories)} trajectories")

# Create figure
fig, axes = plt.subplots(2, 2, figsize=(14, 10))
fig.suptitle('Rectified Flow Trajectory Animation', fontsize=16, fontweight='bold')

ax1 = axes[0, 0]  # X-space 2D
ax2 = axes[0, 1]  # Y-space 2D
ax3 = axes[1, 0]  # Combined evolution
ax4 = axes[1, 1]  # Flow metrics

# Configure axes
ax1.set_title('X-Space Trajectories (x₁ vs x₂)', fontweight='bold')
ax1.set_xlabel('x₁')
ax1.set_ylabel('x₂')
ax1.grid(True, alpha=0.3)

ax2.set_title('Y-Space Trajectories (y₁ vs y₂)', fontweight='bold')
ax2.set_xlabel('y₁')
ax2.set_ylabel('y₂')
ax2.grid(True, alpha=0.3)

ax3.set_title('State Norm Evolution', fontweight='bold')
ax3.set_xlabel('X-norm')
ax3.set_ylabel('Y-norm')
ax3.grid(True, alpha=0.3)

ax4.set_title('Distance from Start', fontweight='bold')
ax4.set_xlabel('Time t')
ax4.set_ylabel('Distance')
ax4.grid(True, alpha=0.3)

# Set axis limits
all_states = np.concatenate([traj['states'] for traj in trajectories])
x_states = all_states[:, :3]
y_states = all_states[:, 3:]

x_min, x_max = x_states.min() - 0.2, x_states.max() + 0.2
y_min, y_max = y_states.min() - 0.2, y_states.max() + 0.2

ax1.set_xlim(x_min, x_max)
ax1.set_ylim(x_min, x_max)
ax2.set_xlim(y_min, y_max)
ax2.set_ylim(y_min, y_max)
ax4.set_xlim(0, 1)

# Initialize animation elements
lines = {'ax1': [], 'ax2': [], 'ax3': [], 'ax4': []}
points = {'ax1': [], 'ax2': [], 'ax3': []}

for traj in trajectories:
    color = traj['color']
    
    # Trajectory lines
    line1, = ax1.plot([], [], color=color, alpha=0.7, linewidth=2)
    line2, = ax2.plot([], [], color=color, alpha=0.7, linewidth=2)
    line3, = ax3.plot([], [], color=color, alpha=0.7, linewidth=2)
    line4, = ax4.plot([], [], color=color, alpha=0.8, linewidth=2)
    
    lines['ax1'].append(line1)
    lines['ax2'].append(line2)
    lines['ax3'].append(line3)
    lines['ax4'].append(line4)
    
    # Current position points
    point1, = ax1.plot([], [], 'o', color=color, markersize=8, markeredgecolor='black')
    point2, = ax2.plot([], [], 'o', color=color, markersize=8, markeredgecolor='black')
    point3, = ax3.plot([], [], 'o', color=color, markersize=8, markeredgecolor='black')
    
    points['ax1'].append(point1)
    points['ax2'].append(point2)
    points['ax3'].append(point3)

# Add start and end markers
for i, traj in enumerate(trajectories):
    # Start markers (green circles)
    ax1.scatter(traj['x0'][0], traj['x0'][1], c='green', s=120, marker='o', 
               alpha=0.8, edgecolors='darkgreen', linewidth=2, zorder=10)
    ax2.scatter(traj['y0'][0], traj['y0'][1], c='green', s=120, marker='o', 
               alpha=0.8, edgecolors='darkgreen', linewidth=2, zorder=10)
    
    # End markers (red X)
    ax1.scatter(traj['x1'][0], traj['x1'][1], c='red', s=150, marker='X', 
               alpha=0.8, edgecolors='darkred', linewidth=2, zorder=10)
    ax2.scatter(traj['y1'][0], traj['y1'][1], c='red', s=150, marker='X', 
               alpha=0.8, edgecolors='darkred', linewidth=2, zorder=10)

# Add legend
from matplotlib.lines import Line2D
legend_elements = [
    Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=10, label='HQ Start'),
    Line2D([0], [0], marker='X', color='w', markerfacecolor='red', markersize=10, label='LQ End')
]
for beta in beta_values:
    legend_elements.append(Line2D([0], [0], color='gray', lw=2, label=f'β = {beta}'))

ax1.legend(handles=legend_elements, loc='upper right', fontsize=8)

# Time display
time_text = fig.text(0.02, 0.95, '', fontsize=14, fontweight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue", alpha=0.8))

print("Creating animation function...")

def animate(frame):
    """Animation function."""
    current_time = ts[frame]
    time_text.set_text(f'Time: t = {current_time:.3f} | Frame: {frame+1}/{len(ts)}')
    
    for i, traj in enumerate(trajectories):
        states = traj['states']
        
        if frame < len(states):
            # Get trajectory up to current frame
            traj_x = states[:frame+1, :3]
            traj_y = states[:frame+1, 3:]
            
            if len(traj_x) > 0:
                # Update trajectory lines
                lines['ax1'][i].set_data(traj_x[:, 0], traj_x[:, 1])
                lines['ax2'][i].set_data(traj_y[:, 0], traj_y[:, 1])
                
                # Combined norm space
                x_norms = np.linalg.norm(traj_x, axis=1)
                y_norms = np.linalg.norm(traj_y, axis=1)
                lines['ax3'][i].set_data(x_norms, y_norms)
                
                # Distance from start
                start_pos = np.concatenate([traj['x0'], traj['y0']])
                distances = []
                for j in range(len(traj_x)):
                    current_pos = np.concatenate([traj_x[j], traj_y[j]])
                    dist = np.linalg.norm(current_pos - start_pos)
                    distances.append(dist)
                lines['ax4'][i].set_data(ts[:frame+1], distances)
                
                # Update current position
                current_x = states[frame, :3]
                current_y = states[frame, 3:]
                
                points['ax1'][i].set_data([current_x[0]], [current_x[1]])
                points['ax2'][i].set_data([current_y[0]], [current_y[1]])
                points['ax3'][i].set_data([np.linalg.norm(current_x)], [np.linalg.norm(current_y)])
    
    return (list(lines['ax1']) + list(lines['ax2']) + list(lines['ax3']) + list(lines['ax4']) +
            list(points['ax1']) + list(points['ax2']) + list(points['ax3']))

# Create and run animation
print("Starting animation...")
anim = animation.FuncAnimation(fig, animate, frames=len(ts), 
                              interval=70, blit=False, repeat=True)

plt.tight_layout()

# Try to save
try:
    print("Saving animation as GIF...")
    anim.save('rectified_flow_fixed.gif', writer='pillow', fps=15, dpi=100)
    print("✓ Animation saved successfully!")
except Exception as e:
    print(f"Could not save animation: {e}")

# Show animation
print("Displaying animation...")
plt.show()

print("\n" + "="*60)
print("ANIMATION COMPLETED SUCCESSFULLY!")
print("="*60)
