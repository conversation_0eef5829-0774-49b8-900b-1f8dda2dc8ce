"""
Training script for PolarFree Flow Matching model
"""

import datetime
import logging
import math
import time
import os.path as osp
import torch

from basicsr.data.data_sampler import EnlargedSampler
from basicsr.data.prefetch_dataloader import CPUPrefetcher, CUDAPrefetcher
from basicsr.models import build_model
from basicsr.utils import (
    AvgTimer, MessageLogger, check_resume, get_env_info, get_root_logger,
    get_time_str, init_tb_logger, init_wandb_logger, make_exp_dirs,
    mkdir_and_rename, scandir
)
from basicsr.utils.options import copy_opt_file, dict2str
from polarfree.utils.options import parse_options

# Import custom data modules to register datasets
from polarfree.data import build_dataloader, build_dataset

# Import custom models and architectures to register them
import polarfree.models
import polarfree.archs

# Import custom schedulers (no registration needed - handled directly in BaseModel)
from polarfree.utils.lr_scheduler import CosineAnnealingRestartCyclicLR


def init_tb_loggers(opt):
    """Initialize tensorboard and wandb loggers."""
    # Initialize wandb logger before tensorboard logger to allow proper sync
    wandb_config = opt['logger'].get('wandb')
    use_wandb = (wandb_config is not None and
                 wandb_config.get('project') is not None and
                 'debug' not in opt['name'])

    if use_wandb:
        assert opt['logger'].get('use_tb_logger') is True, 'should turn on tensorboard when using wandb'
        init_wandb_logger(opt)

    tb_logger = None
    if opt['logger'].get('use_tb_logger') and 'debug' not in opt['name']:
        tb_logger = init_tb_logger(log_dir=osp.join(opt['root_path'], 'tb_logger', opt['name']))
    return tb_logger


def main():
    # Parse options using the project's custom parser
    root_path = osp.abspath(osp.join(__file__, osp.pardir))
    opt, args = parse_options(root_path, is_train=True)
    opt['root_path'] = root_path  # Add root_path to opt

    # Distributed training is already handled in parse_options
    rank = opt['rank']
    world_size = opt['world_size']

    # Set random seed
    seed = opt.get('manual_seed', 1234)
    torch.manual_seed(seed + rank)
    
    # Create experiment directories
    if rank == 0:
        make_exp_dirs(opt)
        if opt.get('logger'):
            log_file = osp.join(opt['path']['log'], f"train_{opt['name']}_{get_time_str()}.log")
        else:
            log_file = None
        logger = get_root_logger(logger_name='polarfree', log_level=logging.INFO, log_file=log_file)
        logger.info(get_env_info())
        logger.info(dict2str(opt))

        # Initialize tensorboard and wandb loggers
        tb_logger = init_tb_loggers(opt)
    else:
        logger = get_root_logger(logger_name='polarfree', log_level=logging.INFO)
        tb_logger = None
    
    # Create datasets and dataloaders
    train_loader = None
    val_loaders = []

    for phase, dataset_opt in opt['datasets'].items():
        if phase == 'train':
            dataset_enlarge_ratio = dataset_opt.get('dataset_enlarge_ratio', 1)
            train_set = build_dataset(dataset_opt)
            train_sampler = EnlargedSampler(train_set, world_size, rank, dataset_enlarge_ratio)
            train_loader = build_dataloader(
                train_set,
                dataset_opt,
                num_gpu=opt['num_gpu'],
                dist=opt['dist'],
                sampler=train_sampler,
                seed=seed
            )

            num_iter_per_epoch = len(train_loader)
            total_iters = int(opt['train']['total_iter'])
            total_epochs = int(total_iters // (num_iter_per_epoch * dataset_enlarge_ratio)) + 1
            logger.info(f'Training statistics:'
                       f'\n\tNumber of train images: {len(train_set)}'
                       f'\n\tDataset enlarge ratio: {dataset_enlarge_ratio}'
                       f'\n\tBatch size per gpu: {dataset_opt["batch_size_per_gpu"]}'
                       f'\n\tWorld size (gpu number): {world_size}'
                       f'\n\tRequire iter number per epoch: {num_iter_per_epoch}'
                       f'\n\tTotal epochs: {total_epochs}; iters: {total_iters}.')

        elif phase.split('_')[0] == 'val':
            val_set = build_dataset(dataset_opt)
            val_loader = build_dataloader(
                val_set, dataset_opt, num_gpu=opt['num_gpu'], dist=opt['dist'], sampler=None, seed=seed
            )
            logger.info(f'Number of val images/folders in {dataset_opt["name"]}: {len(val_set)}')
            val_loaders.append(val_loader)
        else:
            raise ValueError(f'Dataset phase {phase} is not recognized.')
    
    # Create model
    model = build_model(opt)
    
    # Resume training if specified
    resume_state = None
    if hasattr(args, 'auto_resume') and args.auto_resume:
        state_path = osp.join(opt['path']['training_states'], 'latest.state')
        if osp.exists(state_path):
            resume_state = torch.load(state_path)
            logger.info(f'Resuming training from {state_path}')

    if resume_state is None:
        start_epoch = 0
        current_iter = 0
    else:
        start_epoch = resume_state['epoch']
        current_iter = resume_state['iter']
        model.resume_training(resume_state)

    # Create message logger
    msg_logger = MessageLogger(opt, current_iter, tb_logger)
    
    # Create data prefetcher
    prefetch_mode = opt['datasets']['train'].get('prefetch_mode')
    if prefetch_mode is None or prefetch_mode == 'cpu':
        prefetcher = CPUPrefetcher(train_loader)
    elif prefetch_mode == 'cuda':
        prefetcher = CUDAPrefetcher(train_loader, opt)
        logger.info(f'Use {prefetch_mode} prefetch dataloader')
        if opt['datasets']['train'].get('pin_memory') is not True:
            raise ValueError('Please set pin_memory=True for CUDAPrefetcher.')
    else:
        raise ValueError(f'Wrong prefetch_mode {prefetch_mode}.'
                         "Supported ones are: None, 'cuda', 'cpu'.")

    # Training loop
    logger.info(f'Start training from epoch: {start_epoch}, iter: {current_iter}')
    data_timer, iter_timer = AvgTimer(), AvgTimer()
    start_time = time.time()

    for epoch in range(start_epoch, total_epochs + 1):
        train_sampler.set_epoch(epoch)
        prefetcher.reset()
        train_data = prefetcher.next()

        while train_data is not None:
            data_timer.record()
            current_iter += 1

            if current_iter > total_iters:
                break

            # Update learning rate
            model.update_learning_rate(current_iter, warmup_iter=opt['train'].get('warmup_iter', -1))

            # Training
            model.feed_data(train_data)
            model.optimize_parameters(current_iter)
            iter_timer.record()

            if current_iter == 1:
                msg_logger.reset_start_time()

            # Log
            if current_iter % opt['logger']['print_freq'] == 0:
                log_vars = {'epoch': epoch, 'iter': current_iter}
                log_vars.update({'lrs': model.get_current_learning_rate()})
                log_vars.update({'time': iter_timer.get_avg_time(), 'data_time': data_timer.get_avg_time()})
                log_vars.update(model.get_current_log())
                msg_logger(log_vars)

            # Save models and training states
            if current_iter % opt['logger']['save_checkpoint_freq'] == 0:
                logger.info('Saving models and training states.')
                model.save(epoch, current_iter)

            # Validation
            if opt.get('val') is not None and (current_iter % opt['val']['val_freq'] == 0):
                for val_loader in val_loaders:
                    model.validation(val_loader, current_iter, tb_logger, opt['val']['save_img'])

            # Fetch next batch
            data_timer.start()
            iter_timer.start()
            train_data = prefetcher.next()
    
    # End of training
    consumed_time = str(datetime.timedelta(seconds=int(time.time() - start_time)))
    logger.info(f'End of training. Time consumed: {consumed_time}')
    logger.info('Save the latest model.')
    model.save(epoch=-1, current_iter=-1)





if __name__ == '__main__':
    import time
    import datetime
    main()
