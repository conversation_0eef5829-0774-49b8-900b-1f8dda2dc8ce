"""
Simple test for Flow Matching integration
"""

import torch
import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_flow_matching():
    """Test basic flow matching functionality."""
    print("🧪 Testing Basic Flow Matching...")
    
    try:
        from polarfree.archs.flow_matching_arch import PolarFlowMatching
        
        # Create a simple model
        model = PolarFlowMatching(
            in_channels=3,
            out_channels=3,
            condition_dim=0,  # No conditioning
            model_channels=32,
            num_res_blocks=1,
            channel_mult=[1, 2],
            attention_resolutions=[],  # No attention
            num_heads=4,
            dropout=0.0,
            sigma=0.0,
        )
        
        print("✅ Created PolarFlowMatching model")
        
        # Test forward pass
        batch_size = 1
        height, width = 16, 16
        
        x = torch.randn(batch_size, 3, height, width)
        t = torch.rand(batch_size)
        
        with torch.no_grad():
            output = model(x, t)
            print(f"✅ Forward pass: {x.shape} -> {output.shape}")
        
        # Test loss computation
        x0 = torch.randn(batch_size, 3, height, width)
        x1 = torch.randn(batch_size, 3, height, width)
        
        with torch.no_grad():
            loss = model.compute_loss(x0, x1)
            print(f"✅ Loss computation: {loss.item():.4f}")
        
        # Test sampling
        with torch.no_grad():
            samples = model.sample(
                shape=(1, 3, height, width),
                num_steps=5,
                method="euler",
                device="cpu"
            )
            print(f"✅ Sampling: {samples.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_creation():
    """Test model creation with minimal config."""
    print("\n🔧 Testing Model Creation...")
    
    try:
        from polarfree.models.PolarFree_FlowMatching_model import PolarFree_FlowMatching
        
        # Minimal config
        opt = {
            'name': 'test_flow_matching',
            'model_type': 'PolarFree_FlowMatching',
            'num_gpu': 1,
            'dist': False,
            'is_train': False,
            'network_g': {
                'type': 'Transformer',
                'inp_channels': 3,
                'out_channels': 3,
                'dim': 16,
                'num_blocks': [1, 1, 1, 1],
                'num_refinement_blocks': 1,
                'heads': [1, 1, 1, 1],
                'ffn_expansion_factor': 2.66,
                'bias': False,
                'LayerNorm_type': 'WithBias',
                'dual_pixel_task': False,
                'embed_dim': 8,
                'group': 4,
            },
            'network_le': {
                'type': 'latent_encoder_gelu',
                'in_chans': 17,  # Correct channel count
                'embed_dim': 8,
                'block_num': 1,
                'group': 4,
                'stage': 1,
                'patch_expansion': 0.5,
                'channel_expansion': 2,
            },
            'network_flow': {
                'type': 'PolarFlowMatching',
                'in_channels': 8,
                'out_channels': 8,
                'condition_dim': 0,  # No conditioning
                'model_channels': 16,
                'num_res_blocks': 1,
                'channel_mult': [1, 2],
                'attention_resolutions': [],  # No attention
                'num_heads': 2,
                'dropout': 0.0,
                'sigma': 0.0,
                'use_ot': False,
            },
            'flow_matching': {
                'num_steps': 5,
                'solver': 'euler',
                'sigma': 0.0,
                'loss_weight': 1.0,
                'use_conditioning': False,
            },
            'path': {
                'pretrain_network_g': None,
                'pretrain_network_le': None,
                'pretrain_network_flow': None,
            }
        }
        
        # Create model
        model = PolarFree_FlowMatching(opt)
        print("✅ Created PolarFree_FlowMatching model")
        
        # Test data feeding
        dummy_data = {
            'lq_rgb': torch.randn(1, 3, 8, 8),
            'lq_img0': torch.randn(1, 3, 8, 8),
            'lq_img45': torch.randn(1, 3, 8, 8),
            'lq_img90': torch.randn(1, 3, 8, 8),
            'lq_img135': torch.randn(1, 3, 8, 8),
            'lq_aolp': torch.randn(1, 1, 8, 8),
            'lq_dolp': torch.randn(1, 1, 8, 8),
        }
        
        model.feed_data(dummy_data)
        print("✅ Fed data to model")
        
        # Test inference
        model.test()
        print("✅ Ran inference")
        
        # Get results
        visuals = model.get_current_visuals()
        print(f"✅ Got results: {list(visuals.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ Model creation test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🌟 Simple Flow Matching Test")
    print("=" * 50)
    
    tests = [
        ("Basic Flow Matching", test_basic_flow_matching),
        ("Model Creation", test_model_creation),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Flow Matching integration is working!")
    else:
        print("⚠️  Some tests failed.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
