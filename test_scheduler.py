"""
Test script to verify scheduler registration
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_scheduler_registration():
    """Test if custom schedulers can be imported and used directly."""
    print("🧪 Testing Scheduler Import and Usage...")
    
    try:
        # Import custom schedulers directly (no registry needed in BasicSR 1.4.2)
        from polarfree.utils.lr_scheduler import CosineAnnealingRestartCyclicLR, CosineAnnealingRestartLR, MultiStepRestartLR

        print("✅ Successfully imported custom schedulers")

        # Test scheduler creation
        import torch
        import torch.nn as nn

        # Create a dummy model and optimizer
        model = nn.Linear(10, 1)
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)

        # Test CosineAnnealingRestartCyclicLR instantiation
        scheduler1 = CosineAnnealingRestartCyclicLR(
            optimizer=optimizer,
            periods=[100, 200],
            restart_weights=[1, 1],
            eta_mins=[0.0002, 0.000001]
        )
        print("✅ Successfully created CosineAnnealingRestartCyclicLR instance")
        print(f"   Scheduler type: {type(scheduler1)}")
        print(f"   Initial LR: {scheduler1.get_lr()}")

        # Test CosineAnnealingRestartLR instantiation
        scheduler2 = CosineAnnealingRestartLR(
            optimizer=optimizer,
            periods=[100, 200],
            restart_weights=[1, 1],
            eta_min=0.000001
        )
        print("✅ Successfully created CosineAnnealingRestartLR instance")

        # Test MultiStepRestartLR instantiation
        scheduler3 = MultiStepRestartLR(
            optimizer=optimizer,
            milestones=[50, 100],
            gamma=0.5
        )
        print("✅ Successfully created MultiStepRestartLR instance")

        # Test scheduler steps
        scheduler1.step()
        scheduler2.step()
        scheduler3.step()
        print("✅ Successfully executed scheduler steps")

        return True
        
    except Exception as e:
        print(f"❌ Scheduler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_loading():
    """Test if configuration can be loaded with custom scheduler."""
    print("\n🧪 Testing Configuration Loading...")
    
    try:
        import yaml
        
        # Load configuration
        with open('options/train/ours_flow_matching.yml', 'r') as f:
            opt = yaml.safe_load(f)
        
        print("✅ Configuration loaded successfully")
        
        scheduler_config = opt['train']['scheduler']
        print(f"✅ Scheduler config: {scheduler_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ Config loading failed: {e}")
        return False


def main():
    """Run scheduler tests."""
    print("🔍 PolarFree Scheduler Test")
    print("=" * 40)
    
    tests = [
        ("Scheduler Import and Usage", test_scheduler_registration),
        ("Configuration Loading", test_config_loading),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name} test...")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 TEST SUMMARY")
    print("=" * 40)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All scheduler tests passed!")
    else:
        print("⚠️  Some tests failed. Check the error messages above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
