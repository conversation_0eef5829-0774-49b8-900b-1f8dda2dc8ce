#!/usr/bin/env python3
"""
测试数据集注册
Test Dataset Registration
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_dataset_registration():
    """测试数据集是否正确注册"""
    print("🧪 测试数据集注册...")
    
    try:
        # 导入数据集模块
        from polarfree.data import build_dataset
        from basicsr.utils.registry import DATASET_REGISTRY
        
        print("✅ 数据集模块导入成功")
        
        # 检查注册的数据集
        registered_datasets = list(DATASET_REGISTRY._obj_map.keys())
        print(f"✅ 已注册的数据集: {registered_datasets}")
        
        # 检查我们需要的数据集是否注册
        required_datasets = ['PairedImagePolarDataset']
        for dataset_name in required_datasets:
            if dataset_name in registered_datasets:
                print(f"✅ {dataset_name} 已正确注册")
            else:
                print(f"❌ {dataset_name} 未注册")
                return False
        
        # 测试创建数据集配置
        test_opt = {
            'name': 'TestDataset',
            'type': 'PairedImagePolarDataset',
            'dataroot_gt': '/tmp/test_gt',
            'dataroot_lq': '/tmp/test_lq',
            'filename_tmpl': '{}',
            'io_backend': {'type': 'disk'},
            'test_scenes': [],
            'easy_data_ratio': 1,
            'hard_data_ratio': 1,
            'phase': 'train',
            'gt_size': 256
        }
        
        print("✅ 数据集配置创建成功")
        
        # 注意：这里不实际创建数据集，因为路径不存在
        # 但我们可以检查类型是否能被找到
        dataset_class = DATASET_REGISTRY.get('PairedImagePolarDataset')
        print(f"✅ 数据集类获取成功: {dataset_class}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_model_registration():
    """测试模型是否正确注册"""
    print("\n🧪 测试模型注册...")
    
    try:
        # 导入模型模块
        import polarfree.models
        import polarfree.archs
        from basicsr.utils.registry import MODEL_REGISTRY, ARCH_REGISTRY
        
        print("✅ 模型模块导入成功")
        
        # 检查注册的模型
        registered_models = list(MODEL_REGISTRY._obj_map.keys())
        print(f"✅ 已注册的模型: {registered_models}")
        
        # 检查注册的架构
        registered_archs = list(ARCH_REGISTRY._obj_map.keys())
        print(f"✅ 已注册的架构: {registered_archs}")
        
        # 检查我们需要的组件是否注册
        required_models = ['PolarFree_FlowMatching']
        for model_name in required_models:
            if model_name in registered_models:
                print(f"✅ {model_name} 模型已正确注册")
            else:
                print(f"⚠️  {model_name} 模型未注册（可能正常，取决于具体实现）")
        
        required_archs = ['PolarFlowMatching', 'latent_encoder_gelu']
        for arch_name in required_archs:
            if arch_name in registered_archs:
                print(f"✅ {arch_name} 架构已正确注册")
            else:
                print(f"❌ {arch_name} 架构未注册")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_config_loading():
    """测试配置文件加载"""
    print("\n🧪 测试配置文件加载...")
    
    try:
        from polarfree.utils.options import parse_options
        
        # 测试加载配置文件
        config_files = [
            'options/train/ours_flow_matching.yml',
            'options/train/ours_invertible_flow.yml'
        ]
        
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"📄 测试加载配置文件: {config_file}")
                
                # 模拟命令行参数
                import sys
                original_argv = sys.argv
                sys.argv = ['test_script.py', '-opt', config_file]
                
                try:
                    opt = parse_options(is_train=True)
                    print(f"✅ 配置文件加载成功")
                    print(f"   模型类型: {opt.get('model_type', 'Unknown')}")
                    print(f"   数据集类型: {opt['datasets']['train']['type']}")
                except Exception as e:
                    print(f"❌ 配置文件加载失败: {e}")
                finally:
                    sys.argv = original_argv
            else:
                print(f"⚠️  配置文件不存在: {config_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试数据集和模型注册...")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 数据集注册
    if test_dataset_registration():
        success_count += 1
    
    # 测试2: 模型注册
    if test_model_registration():
        success_count += 1
    
    # 测试3: 配置文件加载
    if test_config_loading():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！注册系统工作正常。")
        print("\n💡 现在可以运行训练脚本:")
        print("python train_flow_matching.py -opt options/train/ours_flow_matching.yml")
    else:
        print("❌ 部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()
