#!/usr/bin/env python3
"""
测试可逆流匹配架构
Test Invertible Flow Matching Architecture
"""

import torch
import torch.nn as nn
import sys
import os
import numpy as np

# Add the current directory to Python path
sys.path.insert(0, os.getcwd())

def test_invertible_flow_block():
    """测试可逆流匹配基础块"""
    print("🧪 测试可逆流匹配基础块...")
    
    try:
        from polarfree.archs.invertible_flow_matching_arch import InvertibleFlowBlock
        from polarfree.archs.common import subnet
        
        # 创建基础块
        block = InvertibleFlowBlock(
            channels=64,
            subnet_constructor=subnet('Resnet'),
            time_embed_dim=512,
            condition_dim=64,
            clamp=1.0
        )
        
        print("✅ 可逆流匹配基础块创建成功")
        
        # 测试前向传播
        batch_size = 2
        x = torch.randn(batch_size, 64, 32, 32)
        time_emb = torch.randn(batch_size, 512)
        condition = torch.randn(batch_size, 64)
        
        # 前向传播
        output_forward = block(x, time_emb, condition, rev=False)
        print(f"✅ 前向传播: {x.shape} -> {output_forward.shape}")
        
        # 反向传播
        output_reverse = block(output_forward, time_emb, condition, rev=True)
        print(f"✅ 反向传播: {output_forward.shape} -> {output_reverse.shape}")
        
        # 检查可逆性（应该接近原始输入）
        reconstruction_error = torch.mean((x - output_reverse) ** 2)
        print(f"✅ 重建误差: {reconstruction_error.item():.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_invertible_flow_matching():
    """测试完整的可逆流匹配网络"""
    print("\n🧪 测试完整的可逆流匹配网络...")
    
    try:
        from polarfree.archs.invertible_flow_matching_arch import InvertibleFlowMatching
        
        # 创建网络
        model = InvertibleFlowMatching(
            in_channels=3,
            out_channels=3,
            model_channels=64,
            num_blocks=2,
            condition_dim=64,
            subnet_type='Resnet',
            time_embed_dim=256,
            use_haar_downsampling=True,
            sigma=0.0,
            clamp=1.0
        )
        
        print("✅ 可逆流匹配网络创建成功")
        print(f"   参数数量: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试数据
        batch_size = 2
        height, width = 64, 64
        
        x = torch.randn(batch_size, 3, height, width)
        timesteps = torch.rand(batch_size)
        condition = torch.randn(batch_size, 64)
        
        # 前向传播
        with torch.no_grad():
            output = model(x, timesteps, condition, rev=False)
            print(f"✅ 前向传播: {x.shape} -> {output.shape}")
            
            # 反向传播
            output_rev = model(output, timesteps, condition, rev=True)
            print(f"✅ 反向传播: {output.shape} -> {output_rev.shape}")
            
            # 编码-解码循环测试
            encoded, decoded = model.encode_decode_cycle(x, condition)
            print(f"✅ 编码-解码循环: {x.shape} -> {encoded.shape} -> {decoded.shape}")
            
            cycle_error = torch.mean((x - decoded) ** 2)
            print(f"✅ 循环重建误差: {cycle_error.item():.6f}")
        
        # 测试流匹配损失
        x0 = torch.randn(batch_size, 3, height, width)
        x1 = torch.randn(batch_size, 3, height, width)
        
        with torch.no_grad():
            loss = model.compute_loss(x0, x1, condition)
            print(f"✅ 流匹配损失: {loss.item():.4f}")
        
        # 测试采样
        print("🎲 测试采样...")
        with torch.no_grad():
            samples = model.sample(
                shape=(1, 3, 32, 32),
                num_steps=5,
                method="euler",
                condition=condition[:1],
                device="cpu"
            )
            print(f"✅ 采样: {samples.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_polarfree_invertible_flow():
    """测试PolarFree可逆流匹配模型"""
    print("\n🧪 测试PolarFree可逆流匹配模型...")
    
    try:
        # 模拟配置
        opt = {
            'network_g': {
                'type': 'Transformer',
                'inp_channels': 3,
                'out_channels': 3,
                'dim': 48,
                'num_blocks': [2, 2, 2, 2],
                'num_refinement_blocks': 2,
                'heads': [1, 2, 4, 8],
                'ffn_expansion_factor': 2.66,
                'bias': False,
                'LayerNorm_type': 'WithBias',
                'dual_pixel_task': False,
                'embed_dim': 64,
                'group': 4
            },
            'network_le': {
                'type': 'latent_encoder_gelu',
                'in_chans': 12,
                'embed_dim': 64,
                'block_num': 4,
                'group': 4,
                'stage': 1,
                'patch_expansion': 0.5,
                'channel_expansion': 4
            },
            'network_inv_flow': {
                'type': 'InvertibleFlowMatching',
                'in_channels': 64,
                'out_channels': 64,
                'model_channels': 64,
                'num_blocks': 2,
                'condition_dim': 64,
                'subnet_type': 'Resnet',
                'time_embed_dim': 256,
                'use_haar_downsampling': False,  # 简化测试
                'sigma': 0.0,
                'clamp': 1.0
            },
            'network_flow_condition': {
                'type': 'latent_encoder_gelu',
                'in_chans': 17,
                'embed_dim': 64,
                'block_num': 2,
                'group': 4,
                'stage': 2,
                'patch_expansion': 0.5,
                'channel_expansion': 2
            },
            'invertible_flow': {
                'num_steps': 10,
                'solver': 'euler',
                'sigma': 0.0,
                'loss_weight': 1.0,
                'recon_loss_weight': 0.1,
                'use_conditioning': True
            },
            'path': {},
            'is_train': True,
            'train': {
                'pixel_opt': {
                    'type': 'L1Loss',
                    'loss_weight': 1.0,
                    'reduction': 'mean'
                },
                'optim_g': {
                    'type': 'Adam',
                    'lr': 2e-4,
                    'weight_decay': 0,
                    'betas': [0.9, 0.99]
                }
            }
        }
        
        from polarfree.models.PolarFree_InvertibleFlow_model import PolarFree_InvertibleFlow
        
        # 创建模型
        model = PolarFree_InvertibleFlow(opt)
        print("✅ PolarFree可逆流匹配模型创建成功")
        
        # 创建测试数据
        batch_size = 1
        height, width = 64, 64
        
        dummy_data = {
            'lq_rgb': torch.randn(batch_size, 3, height, width),
            'lq_img0': torch.randn(batch_size, 3, height, width),
            'lq_img45': torch.randn(batch_size, 3, height, width),
            'lq_img90': torch.randn(batch_size, 3, height, width),
            'lq_img135': torch.randn(batch_size, 3, height, width),
            'lq_aolp': torch.randn(batch_size, 1, height, width),
            'lq_dolp': torch.randn(batch_size, 1, height, width),
            'gt_rgb': torch.randn(batch_size, 3, height, width),
        }
        
        # 输入数据
        model.feed_data(dummy_data)
        print("✅ 数据输入成功")
        
        # 前向传播
        model.forward()
        print("✅ 前向传播成功")
        print(f"   输出形状: {model.output.shape}")
        
        # 测试模式
        model.test()
        print("✅ 测试模式成功")
        
        # 获取可视化结果
        visuals = model.get_current_visuals()
        print(f"✅ 可视化结果: {list(visuals.keys())}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 开始测试可逆流匹配架构...")
    
    success_count = 0
    total_tests = 3
    
    # 测试1: 可逆流匹配基础块
    if test_invertible_flow_block():
        success_count += 1
    
    # 测试2: 完整的可逆流匹配网络
    if test_invertible_flow_matching():
        success_count += 1
    
    # 测试3: PolarFree可逆流匹配模型
    if test_polarfree_invertible_flow():
        success_count += 1
    
    print(f"\n📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！可逆流匹配架构工作正常。")
        print("\n💡 使用建议:")
        print("1. 可逆性质提供了精确的双向变换能力")
        print("2. 流匹配提供了连续的生成过程")
        print("3. 结合两者可以获得更好的潜在空间建模")
        print("4. 重建损失利用可逆性质提高训练稳定性")
    else:
        print("❌ 部分测试失败，请检查错误信息。")


if __name__ == "__main__":
    main()
