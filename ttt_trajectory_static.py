import numpy as np
import matplotlib.pyplot as plt
from matplotlib.patches import FancyArrowPatch
from mpl_toolkits.mplot3d import proj3d

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation between two states."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

# Setup
print("="*70)
print("RECTIFIED FLOW TRAJECTORY VISUALIZATION - STATIC VERSION")
print("="*70)

# Parameters
n_trajectories = 12
beta_values = [3, 8, 15]
n_time_steps = 100
ts = np.linspace(0, 1, n_time_steps)

# Generate trajectories
trajectories = []
colors = {
    3: ['#FF6B6B', '#FF8E8E', '#FFB1B1', '#FFD4D4'],   # Red family
    8: ['#4ECDC4', '#70D4CD', '#92DCD6', '#B4E4DF'],   # Teal family  
    15: ['#45B7D1', '#67C5D7', '#89D3DD', '#ABE1E3']   # Blue family
}

print(f"Generating {n_trajectories} trajectories for β = {beta_values}")

traj_id = 0
for beta in beta_values:
    beta_colors = colors[beta]
    n_per_beta = n_trajectories // len(beta_values)
    
    for i in range(n_per_beta):
        np.random.seed(traj_id + beta * 20)
        
        # Create varied initial and final states
        x0 = np.array([4.5 + np.random.uniform(-0.8, 0.8), 
                      4.5 + np.random.uniform(-0.8, 0.8), 
                      4.5 + np.random.uniform(-0.8, 0.8)])
        y0 = np.random.normal(0, 0.2, 3)
        
        x1 = np.array([1.0 + np.random.uniform(-0.4, 0.4), 
                      1.0 + np.random.uniform(-0.4, 0.4), 
                      1.0 + np.random.uniform(-0.4, 0.4)])
        y1 = np.random.normal(0, 1.0, 3)
        
        # Compute full trajectory
        states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
        
        trajectories.append({
            'beta': beta,
            'states': states,
            'color': beta_colors[i % len(beta_colors)],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1,
            'id': traj_id
        })
        traj_id += 1

print(f"Generated {len(trajectories)} trajectories")

# Create comprehensive visualization
fig = plt.figure(figsize=(20, 15))
fig.suptitle('Rectified Flow Trajectory Analysis', fontsize=18, fontweight='bold')

# 1. 3D X-space trajectories
ax1 = fig.add_subplot(3, 4, 1, projection='3d')
ax1.set_title('3D X-Space Trajectories', fontweight='bold')
ax1.set_xlabel('x₁')
ax1.set_ylabel('x₂')
ax1.set_zlabel('x₃')

# 2. 3D Y-space trajectories
ax2 = fig.add_subplot(3, 4, 2, projection='3d')
ax2.set_title('3D Y-Space Trajectories', fontweight='bold')
ax2.set_xlabel('y₁')
ax2.set_ylabel('y₂')
ax2.set_zlabel('y₃')

# 3. 2D X-space projection
ax3 = fig.add_subplot(3, 4, 3)
ax3.set_title('X-Space: x₁ vs x₂', fontweight='bold')
ax3.set_xlabel('x₁')
ax3.set_ylabel('x₂')
ax3.grid(True, alpha=0.3)

# 4. 2D Y-space projection
ax4 = fig.add_subplot(3, 4, 4)
ax4.set_title('Y-Space: y₁ vs y₂', fontweight='bold')
ax4.set_xlabel('y₁')
ax4.set_ylabel('y₂')
ax4.grid(True, alpha=0.3)

# 5. State norm evolution
ax5 = fig.add_subplot(3, 4, 5)
ax5.set_title('State Norm Evolution', fontweight='bold')
ax5.set_xlabel('Time t')
ax5.set_ylabel('L2 Norm')
ax5.grid(True, alpha=0.3)

# 6. Combined state space
ax6 = fig.add_subplot(3, 4, 6)
ax6.set_title('Combined State Space', fontweight='bold')
ax6.set_xlabel('X-norm')
ax6.set_ylabel('Y-norm')
ax6.grid(True, alpha=0.3)

# 7. Velocity evolution
ax7 = fig.add_subplot(3, 4, 7)
ax7.set_title('Velocity Magnitude', fontweight='bold')
ax7.set_xlabel('Time t')
ax7.set_ylabel('Velocity')
ax7.grid(True, alpha=0.3)

# 8. Distance from start
ax8 = fig.add_subplot(3, 4, 8)
ax8.set_title('Distance from Start', fontweight='bold')
ax8.set_xlabel('Time t')
ax8.set_ylabel('Distance')
ax8.grid(True, alpha=0.3)

# 9. Sigma_y evolution
ax9 = fig.add_subplot(3, 4, 9)
ax9.set_title('σ_y(t) for Different β', fontweight='bold')
ax9.set_xlabel('Time t')
ax9.set_ylabel('σ_y(t)')
ax9.grid(True, alpha=0.3)

# 10. Trajectory curvature
ax10 = fig.add_subplot(3, 4, 10)
ax10.set_title('Trajectory Curvature', fontweight='bold')
ax10.set_xlabel('Time t')
ax10.set_ylabel('Curvature')
ax10.grid(True, alpha=0.3)

# 11. Phase portrait
ax11 = fig.add_subplot(3, 4, 11)
ax11.set_title('Phase Portrait (x₁ vs y₁)', fontweight='bold')
ax11.set_xlabel('x₁')
ax11.set_ylabel('y₁')
ax11.grid(True, alpha=0.3)

# 12. Energy evolution
ax12 = fig.add_subplot(3, 4, 12)
ax12.set_title('System "Energy"', fontweight='bold')
ax12.set_xlabel('Time t')
ax12.set_ylabel('Energy')
ax12.grid(True, alpha=0.3)

# Plot sigma_y curves (static reference)
for beta in beta_values:
    sigma_values = [sigma_y(t, beta) for t in ts]
    ax9.plot(ts, sigma_values, linewidth=3, label=f'β = {beta}')
ax9.legend()

print("Plotting trajectories...")

# Plot all trajectories
for traj in trajectories:
    states = traj['states']
    color = traj['color']
    beta = traj['beta']
    
    # Extract coordinates
    x_coords = states[:, :3]
    y_coords = states[:, 3:]
    
    # 1. 3D X-space
    ax1.plot(x_coords[:, 0], x_coords[:, 1], x_coords[:, 2], 
             color=color, alpha=0.7, linewidth=2)
    
    # 2. 3D Y-space
    ax2.plot(y_coords[:, 0], y_coords[:, 1], y_coords[:, 2], 
             color=color, alpha=0.7, linewidth=2)
    
    # 3. 2D X-space
    ax3.plot(x_coords[:, 0], x_coords[:, 1], color=color, alpha=0.7, linewidth=2)
    
    # 4. 2D Y-space
    ax4.plot(y_coords[:, 0], y_coords[:, 1], color=color, alpha=0.7, linewidth=2)
    
    # 5. State norms
    x_norms = np.linalg.norm(x_coords, axis=1)
    y_norms = np.linalg.norm(y_coords, axis=1)
    ax5.plot(ts, x_norms, color=color, alpha=0.6, linewidth=1, linestyle='-')
    ax5.plot(ts, y_norms, color=color, alpha=0.6, linewidth=1, linestyle='--')
    
    # 6. Combined state space
    ax6.plot(x_norms, y_norms, color=color, alpha=0.7, linewidth=2)
    
    # 7. Velocity magnitude
    velocities = np.diff(states, axis=0)
    vel_magnitudes = np.linalg.norm(velocities, axis=1)
    ax7.plot(ts[1:], vel_magnitudes, color=color, alpha=0.7, linewidth=1)
    
    # 8. Distance from start
    start_pos = np.concatenate([traj['x0'], traj['y0']])
    distances = [np.linalg.norm(state - start_pos) for state in states]
    ax8.plot(ts, distances, color=color, alpha=0.7, linewidth=2)
    
    # 10. Trajectory curvature (simplified)
    if len(velocities) > 1:
        accelerations = np.diff(velocities, axis=0)
        curvatures = np.linalg.norm(accelerations, axis=1)
        ax10.plot(ts[2:], curvatures, color=color, alpha=0.7, linewidth=1)
    
    # 11. Phase portrait
    ax11.plot(x_coords[:, 0], y_coords[:, 0], color=color, alpha=0.7, linewidth=2)
    
    # 12. System "energy" (sum of squared norms)
    energies = x_norms**2 + y_norms**2
    ax12.plot(ts, energies, color=color, alpha=0.7, linewidth=2)

print("Adding markers and annotations...")

# Add start/end markers to all relevant plots
for traj in trajectories:
    # Start markers (green)
    ax1.scatter([traj['x0'][0]], [traj['x0'][1]], [traj['x0'][2]], 
               c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax2.scatter([traj['y0'][0]], [traj['y0'][1]], [traj['y0'][2]], 
               c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax3.scatter([traj['x0'][0]], [traj['x0'][1]], 
               c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax4.scatter([traj['y0'][0]], [traj['y0'][1]], 
               c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    ax11.scatter([traj['x0'][0]], [traj['y0'][0]], 
                c='green', s=80, marker='o', alpha=0.9, edgecolors='darkgreen')
    
    # End markers (red)
    ax1.scatter([traj['x1'][0]], [traj['x1'][1]], [traj['x1'][2]], 
               c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    ax2.scatter([traj['y1'][0]], [traj['y1'][1]], [traj['y1'][2]], 
               c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    ax3.scatter([traj['x1'][0]], [traj['x1'][1]], 
               c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    ax4.scatter([traj['y1'][0]], [traj['y1'][1]], 
               c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')
    ax11.scatter([traj['x1'][0]], [traj['y1'][0]], 
                c='red', s=100, marker='X', alpha=0.9, edgecolors='darkred')

# Add legends
from matplotlib.lines import Line2D
legend_elements = [
    Line2D([0], [0], marker='o', color='w', markerfacecolor='green', markersize=8, label='HQ Start'),
    Line2D([0], [0], marker='X', color='w', markerfacecolor='red', markersize=8, label='LQ End'),
    Line2D([0], [0], color='#FF6B6B', lw=3, label='β = 3'),
    Line2D([0], [0], color='#4ECDC4', lw=3, label='β = 8'),
    Line2D([0], [0], color='#45B7D1', lw=3, label='β = 15')
]

ax1.legend(handles=legend_elements, loc='upper right', fontsize=8)

# Add special annotations
ax5.text(0.02, 0.98, 'Solid: X-norm\nDashed: Y-norm', transform=ax5.transAxes, 
         verticalalignment='top', fontsize=8, bbox=dict(boxstyle="round,pad=0.3", facecolor="white", alpha=0.8))

plt.tight_layout()

# Save the plot
plt.savefig('rectified_flow_comprehensive_trajectories.png', dpi=300, bbox_inches='tight')
print("✓ Saved comprehensive trajectory plot")

plt.show()

print("\n" + "="*70)
print("COMPREHENSIVE TRAJECTORY ANALYSIS COMPLETED")
print("="*70)
print("📊 12-Panel Analysis:")
print("  • 3D trajectories in X and Y spaces")
print("  • 2D projections for detailed view")
print("  • State norm evolution over time")
print("  • Combined state space dynamics")
print("  • Velocity and curvature analysis")
print("  • Phase portraits and energy evolution")
print("  • σ_y(t) reference curves")
print("")
print("🎯 Key Insights:")
print("  • Green circles = High-Quality starting states")
print("  • Red X marks = Low-Quality target states")
print("  • Different colors = Different β parameters")
print("  • Smooth trajectories show rectified flow interpolation")
print("="*70)
