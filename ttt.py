import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import multivariate_normal
import numpy as np
import matplotlib.pyplot as plt

# 下面是之前的代码...

def sigma_y(t, beta=10):
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

def entropy_gaussian(cov):
    """多元高斯熵公式"""
    k = cov.shape[0]
    return 0.5 * k * (1 + np.log(2 * np.pi)) + 0.5 * np.log(np.linalg.det(cov))

# 定义HQ和LQ状态（三维）
x0 = np.array([5.0, 5.0, 5.0])
x1 = np.array([1.0, 1.0, 1.0])

y0 = np.zeros(3)
np.random.seed(42)
y1 = np.random.randn(3)

beta = 10
ts = np.linspace(0, 1, 100)
states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])

# 近似速度场（梯度示意）：这里用插值的导数作为速度场演示
v_x = (x1 - x0)  # 恒定速度方向
v_y = (y1 - y0) * (-beta / (1 - ts + beta)**2)[:, None]

# 计算熵变化，假设x和y独立，且y方差随sigma_y变化
entropies = []
for t in ts:
    sigma_y_t = sigma_y(t, beta)
    cov_y = np.eye(3) * sigma_y_t**2
    cov_x = np.eye(3) * (t**2)  # 假设x的“噪声”与t成正比，演示用
    ent = entropy_gaussian(cov_x) + entropy_gaussian(cov_y)
    entropies.append(ent)

plt.figure(figsize=(16,8))

# 绘制x的三个维度变化
plt.subplot(2, 2, 1)
plt.plot(ts, states[:, 0], label='x1')
plt.plot(ts, states[:, 1], label='x2')
plt.plot(ts, states[:, 2], label='x3')
plt.title('主变量 x (3维) 随 t 变化')
plt.xlabel('t')
plt.ylabel('x')
plt.legend()

# 绘制y的三个维度变化
plt.subplot(2, 2, 2)
plt.plot(ts, states[:, 3], label='y1')
plt.plot(ts, states[:, 4], label='y2')
plt.plot(ts, states[:, 5], label='y3')
plt.title('辅助变量 y (3维) 随 t 变化')
plt.xlabel('t')
plt.ylabel('y')
plt.legend()

# 绘制速度场演示（x和y分量）
plt.subplot(2, 2, 3)
plt.plot(ts, np.full_like(ts, v_x[0]), label='v_x1')
plt.plot(ts, np.full_like(ts, v_x[1]), label='v_x2')
plt.plot(ts, np.full_like(ts, v_x[2]), label='v_x3')
plt.title('x的速度场（恒定）')
plt.xlabel('t')
plt.ylabel('速度')
plt.legend()

plt.subplot(2, 2, 4)
plt.plot(ts, v_y[:, 0], label='v_y1')
plt.plot(ts, v_y[:, 1], label='v_y2')
plt.plot(ts, v_y[:, 2], label='v_y3')
plt.title('y的速度场（变化）')
plt.xlabel('t')
plt.ylabel('速度')
plt.legend()

plt.tight_layout()
plt.savefig('resflow_complex_demo.png')
plt.close()

# 输出熵变化示意
print("熵随 t 变化（近似）:", entropies[:5], "...", entropies[-5:])
