import numpy as np
import matplotlib.pyplot as plt
from scipy.stats import multivariate_normal

def sigma_y(t, beta=10):
    """
    Compute the interpolation weight for auxiliary variable y.

    Args:
        t: Time parameter in [0, 1]
        beta: Sharpness parameter controlling the transition

    Returns:
        Weight for y interpolation
    """
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """
    Rectified flow interpolation between two states.

    Args:
        t: Time parameter in [0, 1]
        x0, x1: Initial and final states for main variable x
        y0, y1: Initial and final states for auxiliary variable y
        beta: Sharpness parameter for y interpolation

    Returns:
        Concatenated interpolated state [x_t, y_t]
    """
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1

    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1

    return np.concatenate([x_t, y_t])

def entropy_gaussian(cov):
    """
    Compute entropy of multivariate Gaussian distribution.

    Args:
        cov: Covariance matrix

    Returns:
        Differential entropy in nats
    """
    k = cov.shape[0]
    det_cov = np.linalg.det(cov)
    if det_cov <= 0:
        return float('inf')  # Handle degenerate case
    return 0.5 * k * (1 + np.log(2 * np.pi)) + 0.5 * np.log(det_cov)

def compute_velocity_field(t, x0, x1, y0, y1, beta=10):
    """
    Compute the velocity field (time derivative) of the rectified flow.

    Args:
        t: Time parameter
        x0, x1: Initial and final states for x
        y0, y1: Initial and final states for y
        beta: Sharpness parameter

    Returns:
        Velocity field [v_x, v_y]
    """
    # Velocity for x (constant)
    v_x = x1 - x0

    # Velocity for y (time-dependent)
    sigma_y_prime = beta / (1 - t + beta)**2  # Derivative of sigma_y
    v_y = (y1 - y0) * sigma_y_prime

    return v_x, v_y

# Define High-Quality (HQ) and Low-Quality (LQ) states (3D)
print("=== Rectified Flow Demonstration ===")
print("Interpolating between High-Quality (HQ) and Low-Quality (LQ) states")
# Initial state (t=0): High-quality state
x0 = np.array([5.0, 5.0, 5.0])  # HQ main variables
y0 = np.zeros(3)                # HQ auxiliary variables

# Final state (t=1): Low-quality state
x1 = np.array([1.0, 1.0, 1.0])  # LQ main variables
np.random.seed(42)
y1 = np.random.randn(3)         # LQ auxiliary variables (noisy)

print(f"Initial HQ state: x0={x0}, y0={y0}")
print(f"Final LQ state: x1={x1}, y1={y1}")

# Parameters
beta = 10  # Sharpness parameter for auxiliary variable interpolation
ts = np.linspace(0, 1, 100)  # Time steps from 0 to 1

# Compute interpolated states along the flow
states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])

# Compute velocity fields using the improved function
velocities_x = []
velocities_y = []
for t in ts:
    v_x, v_y = compute_velocity_field(t, x0, x1, y0, y1, beta)
    velocities_x.append(v_x)
    velocities_y.append(v_y)

velocities_x = np.array(velocities_x)
velocities_y = np.array(velocities_y)

# Compute entropy evolution (assuming independence and Gaussian distributions)
entropies = []
sigma_y_values = []
for t in ts:
    sigma_y_t = sigma_y(t, beta)
    sigma_y_values.append(sigma_y_t)

    # Covariance matrices (simplified assumptions)
    cov_y = np.eye(3) * (sigma_y_t * 0.1)**2  # Auxiliary variable uncertainty
    cov_x = np.eye(3) * (t**2)  # 假设x的“噪声”与t成正比，演示用
    ent = entropy_gaussian(cov_x) + entropy_gaussian(cov_y)
    entropies.append(ent)

print(f"Beta parameter: {beta}")
print(f"Number of time steps: {len(ts)}")
print(f"Entropy range: [{min(entropies):.3f}, {max(entropies):.3f}]")

# Create comprehensive visualization
plt.figure(figsize=(20, 12))

# Plot 1: Main variable x evolution (3D)
plt.subplot(3, 3, 1)
plt.plot(ts, states[:, 0], label='x₁', linewidth=2, color='blue')
plt.plot(ts, states[:, 1], label='x₂', linewidth=2, color='red')
plt.plot(ts, states[:, 2], label='x₃', linewidth=2, color='green')
plt.title('Main Variables x (3D) Evolution', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('x values')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 2: Auxiliary variable y evolution (3D)
plt.subplot(3, 3, 2)
plt.plot(ts, states[:, 3], label='y₁', linewidth=2, color='cyan')
plt.plot(ts, states[:, 4], label='y₂', linewidth=2, color='magenta')
plt.plot(ts, states[:, 5], label='y₃', linewidth=2, color='orange')
plt.title('Auxiliary Variables y (3D) Evolution', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('y values')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 3: Velocity field for x (constant)
plt.subplot(3, 3, 3)
plt.plot(ts, velocities_x[:, 0], label='v_x₁', linewidth=2, color='blue')
plt.plot(ts, velocities_x[:, 1], label='v_x₂', linewidth=2, color='red')
plt.plot(ts, velocities_x[:, 2], label='v_x₃', linewidth=2, color='green')
plt.title('Velocity Field for x (Constant)', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('Velocity')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 4: Velocity field for y (time-dependent)
plt.subplot(3, 3, 4)
plt.plot(ts, velocities_y[:, 0], label='v_y₁', linewidth=2, color='cyan')
plt.plot(ts, velocities_y[:, 1], label='v_y₂', linewidth=2, color='magenta')
plt.plot(ts, velocities_y[:, 2], label='v_y₃', linewidth=2, color='orange')
plt.title('Velocity Field for y (Time-dependent)', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('Velocity')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 5: Sigma_y evolution
plt.subplot(3, 3, 5)
plt.plot(ts, sigma_y_values, linewidth=3, color='purple')
plt.title('σ_y(t) Evolution', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('σ_y(t)')
plt.grid(True, alpha=0.3)

# Plot 6: Entropy evolution
plt.subplot(3, 3, 6)
plt.plot(ts, entropies, linewidth=3, color='darkred')
plt.title('System Entropy Evolution', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('Entropy (nats)')
plt.grid(True, alpha=0.3)

# Plot 7: 2D trajectory in x-space
plt.subplot(3, 3, 7)
plt.plot(states[:, 0], states[:, 1], linewidth=2, color='blue', alpha=0.7)
plt.scatter(states[0, 0], states[0, 1], color='green', s=100, label='HQ Start', marker='o')
plt.scatter(states[-1, 0], states[-1, 1], color='red', s=100, label='LQ End', marker='x')
plt.title('Trajectory in x₁-x₂ Space', fontsize=12, fontweight='bold')
plt.xlabel('x₁')
plt.ylabel('x₂')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 8: 2D trajectory in y-space
plt.subplot(3, 3, 8)
plt.plot(states[:, 3], states[:, 4], linewidth=2, color='cyan', alpha=0.7)
plt.scatter(states[0, 3], states[0, 4], color='green', s=100, label='HQ Start', marker='o')
plt.scatter(states[-1, 3], states[-1, 4], color='red', s=100, label='LQ End', marker='x')
plt.title('Trajectory in y₁-y₂ Space', fontsize=12, fontweight='bold')
plt.xlabel('y₁')
plt.ylabel('y₂')
plt.legend()
plt.grid(True, alpha=0.3)

# Plot 9: Combined state norm evolution
plt.subplot(3, 3, 9)
x_norms = np.linalg.norm(states[:, :3], axis=1)
y_norms = np.linalg.norm(states[:, 3:], axis=1)
plt.plot(ts, x_norms, label='||x||₂', linewidth=2, color='blue')
plt.plot(ts, y_norms, label='||y||₂', linewidth=2, color='cyan')
plt.title('State Vector Norms', fontsize=12, fontweight='bold')
plt.xlabel('Time t')
plt.ylabel('L2 Norm')
plt.legend()
plt.grid(True, alpha=0.3)

plt.tight_layout()
plt.savefig('rectified_flow_comprehensive_demo.png', dpi=300, bbox_inches='tight')
plt.show()

# Print comprehensive summary
print("\n" + "="*60)
print("RECTIFIED FLOW ANALYSIS SUMMARY")
print("="*60)
print(f"Initial HQ state norms: ||x₀||₂ = {np.linalg.norm(x0):.3f}, ||y₀||₂ = {np.linalg.norm(y0):.3f}")
print(f"Final LQ state norms:   ||x₁||₂ = {np.linalg.norm(x1):.3f}, ||y₁||₂ = {np.linalg.norm(y1):.3f}")
print(f"Beta parameter (sharpness): {beta}")
print(f"σ_y(0) = {sigma_y_values[0]:.6f}, σ_y(1) = {sigma_y_values[-1]:.6f}")
print(f"Entropy evolution: {min(entropies):.3f} → {max(entropies):.3f} nats")
print(f"Total entropy change: {entropies[-1] - entropies[0]:.3f} nats")

# Velocity analysis
max_v_x = np.max(np.abs(velocities_x))
max_v_y = np.max(np.abs(velocities_y))
print(f"Maximum velocity magnitudes: |v_x| = {max_v_x:.3f}, |v_y| = {max_v_y:.3f}")
print("="*60)
