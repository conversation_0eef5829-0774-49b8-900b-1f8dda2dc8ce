import numpy as np
import matplotlib.pyplot as plt
import matplotlib.animation as animation
from mpl_toolkits.mplot3d import Axes3D
from scipy.stats import multivariate_normal

def sigma_y(t, beta=10):
    """Compute the interpolation weight for auxiliary variable y."""
    return beta / (1 - t + beta)

def interpolate(t, x0, x1, y0, y1, beta=10):
    """Rectified flow interpolation between two states."""
    # Linear interpolation for main variable x
    alpha_x = 1 - t
    sigma_x = t
    x_t = alpha_x * x0 + sigma_x * x1
    
    # Non-linear interpolation for auxiliary variable y
    sigma_y_t = sigma_y(t, beta)
    alpha_y = 1 - sigma_y_t
    y_t = alpha_y * y0 + sigma_y_t * y1
    
    return np.concatenate([x_t, y_t])

def compute_velocity_field(t, x0, x1, y0, y1, beta=10):
    """Compute the velocity field (time derivative) of the rectified flow."""
    # Velocity for x (constant)
    v_x = x1 - x0
    
    # Velocity for y (time-dependent)
    sigma_y_prime = beta / (1 - t + beta)**2  # Derivative of sigma_y
    v_y = (y1 - y0) * sigma_y_prime
    
    return v_x, v_y

# Animation parameters
print("="*60)
print("RECTIFIED FLOW TRAJECTORY ANIMATION")
print("="*60)

# Multiple trajectories with different parameters
n_trajectories = 8
beta_values = [5, 10, 20]
n_time_steps = 200
ts = np.linspace(0, 1, n_time_steps)

# Base states
x0_base = np.array([5.0, 5.0, 5.0])
y0_base = np.zeros(3)
x1_base = np.array([1.0, 1.0, 1.0])

# Generate multiple trajectories
trajectories = []
colors = plt.cm.Set3(np.linspace(0, 1, n_trajectories * len(beta_values)))
color_idx = 0

print(f"Generating {n_trajectories} trajectories for each β = {beta_values}")

for beta in beta_values:
    for traj_id in range(n_trajectories):
        # Create varied initial and final states
        np.random.seed(traj_id + beta * 100)
        
        # Perturb states for variety
        x0 = x0_base + np.random.normal(0, 0.3, 3)
        y0 = y0_base + np.random.normal(0, 0.1, 3)
        x1 = x1_base + np.random.normal(0, 0.3, 3)
        y1 = np.random.randn(3) * 0.8
        
        # Compute trajectory
        states = np.array([interpolate(t, x0, x1, y0, y1, beta) for t in ts])
        
        # Compute velocities
        velocities = []
        for t in ts:
            v_x, v_y = compute_velocity_field(t, x0, x1, y0, y1, beta)
            velocities.append(np.concatenate([v_x, v_y]))
        velocities = np.array(velocities)
        
        trajectories.append({
            'beta': beta,
            'states': states,
            'velocities': velocities,
            'color': colors[color_idx],
            'x0': x0, 'x1': x1, 'y0': y0, 'y1': y1
        })
        color_idx += 1

print(f"Generated {len(trajectories)} total trajectories")

# Create the animation
fig = plt.figure(figsize=(20, 12))

# Set up subplots
ax1 = plt.subplot(2, 3, 1, projection='3d')  # 3D x-space
ax2 = plt.subplot(2, 3, 2, projection='3d')  # 3D y-space
ax3 = plt.subplot(2, 3, 3)                   # 2D x1-x2 plane
ax4 = plt.subplot(2, 3, 4)                   # 2D y1-y2 plane
ax5 = plt.subplot(2, 3, 5)                   # Time series
ax6 = plt.subplot(2, 3, 6)                   # Velocity field

# Initialize plots
lines_3d_x = []
lines_3d_y = []
lines_2d_x = []
lines_2d_y = []
points_3d_x = []
points_3d_y = []
points_2d_x = []
points_2d_y = []

# Initialize 3D trajectories
for traj in trajectories:
    # 3D x-space
    line_x, = ax1.plot([], [], [], color=traj['color'], alpha=0.7, linewidth=1.5)
    point_x, = ax1.plot([], [], [], 'o', color=traj['color'], markersize=6)
    lines_3d_x.append(line_x)
    points_3d_x.append(point_x)
    
    # 3D y-space
    line_y, = ax2.plot([], [], [], color=traj['color'], alpha=0.7, linewidth=1.5)
    point_y, = ax2.plot([], [], [], 'o', color=traj['color'], markersize=6)
    lines_3d_y.append(line_y)
    points_3d_y.append(point_y)
    
    # 2D x1-x2 plane
    line_2d_x, = ax3.plot([], [], color=traj['color'], alpha=0.7, linewidth=1.5)
    point_2d_x, = ax3.plot([], [], 'o', color=traj['color'], markersize=6)
    lines_2d_x.append(line_2d_x)
    points_2d_x.append(point_2d_x)
    
    # 2D y1-y2 plane
    line_2d_y, = ax4.plot([], [], color=traj['color'], alpha=0.7, linewidth=1.5)
    point_2d_y, = ax4.plot([], [], 'o', color=traj['color'], markersize=6)
    lines_2d_y.append(line_2d_y)
    points_2d_y.append(point_2d_y)

# Time series lines (one for each beta)
time_lines = []
for i, beta in enumerate(beta_values):
    line, = ax5.plot([], [], linewidth=3, label=f'β = {beta}', 
                    color=['blue', 'red', 'green'][i])
    time_lines.append(line)

# Velocity field arrows
velocity_arrows = []

# Set up axes
ax1.set_title('3D Trajectories in X-Space', fontweight='bold')
ax1.set_xlabel('x₁')
ax1.set_ylabel('x₂')
ax1.set_zlabel('x₃')

ax2.set_title('3D Trajectories in Y-Space', fontweight='bold')
ax2.set_xlabel('y₁')
ax2.set_ylabel('y₂')
ax2.set_zlabel('y₃')

ax3.set_title('2D Projection: x₁-x₂ Plane', fontweight='bold')
ax3.set_xlabel('x₁')
ax3.set_ylabel('x₂')
ax3.grid(True, alpha=0.3)

ax4.set_title('2D Projection: y₁-y₂ Plane', fontweight='bold')
ax4.set_xlabel('y₁')
ax4.set_ylabel('y₂')
ax4.grid(True, alpha=0.3)

ax5.set_title('System Entropy Evolution', fontweight='bold')
ax5.set_xlabel('Time t')
ax5.set_ylabel('Entropy (nats)')
ax5.legend()
ax5.grid(True, alpha=0.3)

ax6.set_title('Velocity Field Magnitude', fontweight='bold')
ax6.set_xlabel('Time t')
ax6.set_ylabel('Velocity Magnitude')
ax6.grid(True, alpha=0.3)

# Set axis limits
all_x_states = np.concatenate([traj['states'][:, :3] for traj in trajectories])
all_y_states = np.concatenate([traj['states'][:, 3:] for traj in trajectories])

x_min, x_max = all_x_states.min() - 0.5, all_x_states.max() + 0.5
y_min, y_max = all_y_states.min() - 0.5, all_y_states.max() + 0.5

ax1.set_xlim(x_min, x_max)
ax1.set_ylim(x_min, x_max)
ax1.set_zlim(x_min, x_max)

ax2.set_xlim(y_min, y_max)
ax2.set_ylim(y_min, y_max)
ax2.set_zlim(y_min, y_max)

ax3.set_xlim(x_min, x_max)
ax3.set_ylim(x_min, x_max)

ax4.set_xlim(y_min, y_max)
ax4.set_ylim(y_min, y_max)

ax5.set_xlim(0, 1)
ax6.set_xlim(0, 1)

# Add text for current time
time_text = fig.text(0.02, 0.95, '', fontsize=14, fontweight='bold')
beta_text = fig.text(0.02, 0.90, f'β values: {beta_values}', fontsize=12)

print("Setting up animation...")

def animate(frame):
    """Animation function called for each frame."""
    current_time = ts[frame]
    time_text.set_text(f'Time: t = {current_time:.3f}')

    # Update trajectories
    for i, traj in enumerate(trajectories):
        states = traj['states']

        # Update 3D x-space trajectory
        x_traj = states[:frame+1, :3]
        if len(x_traj) > 0:
            lines_3d_x[i].set_data_3d(x_traj[:, 0], x_traj[:, 1], x_traj[:, 2])
            # Current point
            if frame < len(states):
                points_3d_x[i].set_data_3d([states[frame, 0]], [states[frame, 1]], [states[frame, 2]])

        # Update 3D y-space trajectory
        y_traj = states[:frame+1, 3:]
        if len(y_traj) > 0:
            lines_3d_y[i].set_data_3d(y_traj[:, 0], y_traj[:, 1], y_traj[:, 2])
            # Current point
            if frame < len(states):
                points_3d_y[i].set_data_3d([states[frame, 3]], [states[frame, 4]], [states[frame, 5]])

        # Update 2D x1-x2 projection
        if len(x_traj) > 0:
            lines_2d_x[i].set_data(x_traj[:, 0], x_traj[:, 1])
            if frame < len(states):
                points_2d_x[i].set_data([states[frame, 0]], [states[frame, 1]])

        # Update 2D y1-y2 projection
        if len(y_traj) > 0:
            lines_2d_y[i].set_data(y_traj[:, 0], y_traj[:, 1])
            if frame < len(states):
                points_2d_y[i].set_data([states[frame, 3]], [states[frame, 4]])

    # Update time series (entropy evolution)
    for i, beta in enumerate(beta_values):
        beta_trajectories = [traj for traj in trajectories if traj['beta'] == beta]
        if beta_trajectories and frame > 0:
            # Compute average entropy for this beta at current time
            entropies = []
            for traj in beta_trajectories:
                # Simple entropy approximation based on state norms
                x_norm = np.linalg.norm(traj['states'][frame, :3])
                y_norm = np.linalg.norm(traj['states'][frame, 3:])
                entropy_approx = 0.5 * np.log(2 * np.pi * (0.1 + current_time * 0.1)**2) * 3 + \
                               0.5 * np.log(2 * np.pi * (sigma_y(current_time, beta) * 0.1)**2) * 3
                entropies.append(entropy_approx)

            avg_entropy = np.mean(entropies)
            time_lines[i].set_data(ts[:frame+1], [avg_entropy] * (frame+1))

    # Update velocity field visualization
    if frame > 0:
        ax6.clear()
        ax6.set_title('Velocity Field Magnitude', fontweight='bold')
        ax6.set_xlabel('Time t')
        ax6.set_ylabel('Velocity Magnitude')
        ax6.grid(True, alpha=0.3)

        for i, beta in enumerate(beta_values):
            beta_trajectories = [traj for traj in trajectories if traj['beta'] == beta]
            if beta_trajectories:
                # Compute average velocity magnitude for this beta
                vel_mags = []
                for traj in beta_trajectories:
                    if frame < len(traj['velocities']):
                        vel_mag = np.linalg.norm(traj['velocities'][frame])
                        vel_mags.append(vel_mag)

                if vel_mags:
                    avg_vel_mag = np.mean(vel_mags)
                    ax6.plot(ts[:frame+1], [avg_vel_mag] * (frame+1),
                            linewidth=3, label=f'β = {beta}',
                            color=['blue', 'red', 'green'][i])

        ax6.legend()
        ax6.set_xlim(0, 1)

    return (lines_3d_x + lines_3d_y + lines_2d_x + lines_2d_y +
            points_3d_x + points_3d_y + points_2d_x + points_2d_y + time_lines)

# Create animation
print("Creating animation...")
anim = animation.FuncAnimation(fig, animate, frames=len(ts), interval=50, blit=False, repeat=True)

# Add start/end markers
for traj in trajectories:
    # Start markers (green)
    ax1.scatter([traj['x0'][0]], [traj['x0'][1]], [traj['x0'][2]],
               c='green', s=100, marker='o', alpha=0.8, label='HQ Start' if traj == trajectories[0] else "")
    ax2.scatter([traj['y0'][0]], [traj['y0'][1]], [traj['y0'][2]],
               c='green', s=100, marker='o', alpha=0.8)
    ax3.scatter([traj['x0'][0]], [traj['x0'][1]],
               c='green', s=100, marker='o', alpha=0.8)
    ax4.scatter([traj['y0'][0]], [traj['y0'][1]],
               c='green', s=100, marker='o', alpha=0.8)

    # End markers (red)
    ax1.scatter([traj['x1'][0]], [traj['x1'][1]], [traj['x1'][2]],
               c='red', s=100, marker='x', alpha=0.8, label='LQ End' if traj == trajectories[0] else "")
    ax2.scatter([traj['y1'][0]], [traj['y1'][1]], [traj['y1'][2]],
               c='red', s=100, marker='x', alpha=0.8)
    ax3.scatter([traj['x1'][0]], [traj['x1'][1]],
               c='red', s=100, marker='x', alpha=0.8)
    ax4.scatter([traj['y1'][0]], [traj['y1'][1]],
               c='red', s=100, marker='x', alpha=0.8)

# Add legends
ax1.legend()
ax2.legend()

plt.tight_layout()

# Save animation
print("Saving animation as GIF...")
try:
    anim.save('rectified_flow_animation.gif', writer='pillow', fps=20, dpi=150)
    print("✓ Animation saved as 'rectified_flow_animation.gif'")
except Exception as e:
    print(f"Could not save GIF: {e}")
    print("Trying to save as MP4...")
    try:
        anim.save('rectified_flow_animation.mp4', writer='ffmpeg', fps=20, dpi=150)
        print("✓ Animation saved as 'rectified_flow_animation.mp4'")
    except Exception as e2:
        print(f"Could not save MP4: {e2}")
        print("Displaying animation instead...")

# Show animation
print("Displaying animation...")
plt.show()

print("\n" + "="*60)
print("ANIMATION FEATURES:")
print("="*60)
print("• 3D trajectories in both X and Y spaces")
print("• 2D projections showing x₁-x₂ and y₁-y₂ planes")
print("• Real-time entropy evolution")
print("• Dynamic velocity field visualization")
print("• Multiple trajectories with different β parameters")
print("• Start (green circles) and end (red X) markers")
print("• Smooth interpolation showing rectified flow dynamics")
print("="*60)
