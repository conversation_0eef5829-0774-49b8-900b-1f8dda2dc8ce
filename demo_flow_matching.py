"""
Demo script for PolarFree Flow Matching
Shows how to use the new flow matching capabilities
"""

import torch
import torch.nn as nn
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import argparse

# Import PolarFree components
from polarfree.archs.flow_matching_arch import PolarFlowMatching, ConditionalFlowMatching
from polarfree.utils.pretrained_loader import load_pretrained_model, PretrainedModelLoader


def demo_flow_matching_basics():
    """Demonstrate basic flow matching functionality."""
    print("🚀 Demo: Basic Flow Matching")
    print("="*50)
    
    # Create a simple flow matching model
    model = PolarFlowMatching(
        in_channels=3,
        out_channels=3,
        condition_dim=64,
        model_channels=64,
        num_res_blocks=2,
        channel_mult=[1, 2, 4],
        attention_resolutions=[16, 8],
        num_heads=4,
        dropout=0.0,
        sigma=0.0,
    )
    
    print(f"✅ Created PolarFlowMatching model")
    print(f"   Parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # Create dummy data
    batch_size = 2
    height, width = 64, 64
    
    # Source and target samples
    x0 = torch.randn(batch_size, 3, height, width)  # Noise
    x1 = torch.randn(batch_size, 3, height, width)  # Target
    condition = torch.randn(batch_size, 64)  # Conditioning
    
    print(f"✅ Created dummy data: {x0.shape}")
    
    # Compute flow matching loss
    with torch.no_grad():
        loss = model.compute_loss(x0, x1, condition)
        print(f"✅ Flow matching loss: {loss.item():.4f}")
    
    # Sample from the model
    with torch.no_grad():
        samples = model.sample(
            shape=(batch_size, 3, height, width),
            condition=condition,
            num_steps=25,
            method="euler",
            device="cpu"
        )
        print(f"✅ Generated samples: {samples.shape}")
    
    print("✨ Basic flow matching demo completed!\n")


def demo_pretrained_loading():
    """Demonstrate pretrained model loading."""
    print("📦 Demo: Pretrained Model Loading")
    print("="*50)
    
    # Create target model
    target_model = PolarFlowMatching(
        in_channels=3,
        out_channels=3,
        condition_dim=64,
        model_channels=128,
        num_res_blocks=2,
    )
    
    print(f"✅ Created target model")
    
    # Demo different loading methods
    loader = PretrainedModelLoader(cache_dir="./demo_cache")
    
    # 1. Load from local file (if exists)
    local_path = "models/demo_model.pth"
    if Path(local_path).exists():
        try:
            model_data = loader.load_from_local(local_path)
            print(f"✅ Loaded from local: {local_path}")
        except Exception as e:
            print(f"❌ Failed to load from local: {e}")
    else:
        print(f"ℹ️  Local model not found: {local_path}")
    
    # 2. Create a dummy pretrained model for demo
    print("✅ Creating dummy pretrained weights...")
    dummy_weights = {
        'unet.time_embed.0.weight': torch.randn(512, 128),
        'unet.time_embed.0.bias': torch.randn(512),
        'unet.input_blocks.0.weight': torch.randn(128, 3, 3, 3),
        'unet.out.2.weight': torch.randn(3, 128, 3, 3),
        'unet.out.2.bias': torch.randn(3),
    }
    
    # Save dummy weights
    dummy_path = "./demo_cache/dummy_model.pth"
    Path("./demo_cache").mkdir(exist_ok=True)
    torch.save(dummy_weights, dummy_path)
    
    # Load dummy weights
    try:
        model_data = loader.load_from_local(dummy_path)
        print(f"✅ Loaded dummy weights: {len(model_data['state_dict'])} parameters")
        
        # Adapt to target model
        from polarfree.utils.pretrained_loader import ModelAdapter
        adapter = ModelAdapter()
        adapted_dict = adapter.adapt_unet_to_flow_matching(
            model_data['state_dict'], 
            target_model, 
            strict=False
        )
        print(f"✅ Adapted weights: {len(adapted_dict)} parameters")
        
    except Exception as e:
        print(f"❌ Failed to load dummy weights: {e}")
    
    print("✨ Pretrained loading demo completed!\n")


def demo_training_simulation():
    """Simulate training process."""
    print("🎯 Demo: Training Simulation")
    print("="*50)
    
    # Create model
    model = PolarFlowMatching(
        in_channels=3,
        out_channels=3,
        condition_dim=32,
        model_channels=64,
        num_res_blocks=1,
        channel_mult=[1, 2],
        attention_resolutions=[16],
        num_heads=4,
    )
    
    # Create optimizer
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-4)
    
    print(f"✅ Created model and optimizer")
    
    # Simulate training data
    batch_size = 4
    height, width = 32, 32
    num_steps = 10
    
    losses = []
    
    print(f"🏃 Running {num_steps} training steps...")
    
    for step in range(num_steps):
        # Generate random training data
        x0 = torch.randn(batch_size, 3, height, width)
        x1 = torch.randn(batch_size, 3, height, width)
        condition = torch.randn(batch_size, 32)
        
        # Forward pass
        optimizer.zero_grad()
        loss = model.compute_loss(x0, x1, condition)
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        losses.append(loss.item())
        
        if step % 2 == 0:
            print(f"   Step {step+1:2d}: Loss = {loss.item():.4f}")
    
    print(f"✅ Training simulation completed")
    print(f"   Final loss: {losses[-1]:.4f}")
    print(f"   Loss reduction: {losses[0] - losses[-1]:.4f}")
    
    # Plot loss curve
    try:
        plt.figure(figsize=(8, 4))
        plt.plot(losses, 'b-', linewidth=2, marker='o')
        plt.title('Flow Matching Training Loss')
        plt.xlabel('Step')
        plt.ylabel('Loss')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # Save plot
        plot_path = "./demo_cache/training_loss.png"
        plt.savefig(plot_path, dpi=150, bbox_inches='tight')
        print(f"✅ Loss plot saved: {plot_path}")
        plt.close()
        
    except Exception as e:
        print(f"❌ Failed to create plot: {e}")
    
    print("✨ Training simulation completed!\n")


def demo_inference_comparison():
    """Compare different inference settings."""
    print("⚡ Demo: Inference Comparison")
    print("="*50)
    
    # Create model
    model = PolarFlowMatching(
        in_channels=3,
        out_channels=3,
        condition_dim=16,
        model_channels=32,
        num_res_blocks=1,
        channel_mult=[1, 2],
    )
    
    # Test data
    batch_size = 1
    height, width = 32, 32
    condition = torch.randn(batch_size, 16)
    
    print(f"✅ Created model for inference testing")
    
    # Test different settings
    settings = [
        {"num_steps": 10, "method": "euler"},
        {"num_steps": 25, "method": "euler"},
        {"num_steps": 50, "method": "euler"},
        {"num_steps": 25, "method": "rk4"},
    ]
    
    results = []
    
    for i, setting in enumerate(settings):
        print(f"🔄 Testing setting {i+1}: {setting}")
        
        # Measure time
        import time
        start_time = time.time()
        
        with torch.no_grad():
            samples = model.sample(
                shape=(batch_size, 3, height, width),
                condition=condition,
                num_steps=setting["num_steps"],
                method=setting["method"],
                device="cpu"
            )
        
        elapsed_time = time.time() - start_time
        
        # Calculate some basic statistics
        mean_val = samples.mean().item()
        std_val = samples.std().item()
        
        result = {
            "setting": setting,
            "time": elapsed_time,
            "mean": mean_val,
            "std": std_val,
            "shape": samples.shape
        }
        results.append(result)
        
        print(f"   ⏱️  Time: {elapsed_time:.3f}s")
        print(f"   📊 Stats: mean={mean_val:.3f}, std={std_val:.3f}")
    
    # Summary
    print("\n📋 Summary:")
    print("Setting".ljust(25) + "Time (s)".ljust(10) + "Mean".ljust(10) + "Std")
    print("-" * 50)
    for result in results:
        setting_str = f"{result['setting']['method']}-{result['setting']['num_steps']}"
        print(f"{setting_str:<25}{result['time']:<10.3f}{result['mean']:<10.3f}{result['std']:.3f}")
    
    print("✨ Inference comparison completed!\n")


def main():
    parser = argparse.ArgumentParser(description="PolarFree Flow Matching Demo")
    parser.add_argument(
        "--demo",
        choices=["all", "basics", "pretrained", "training", "inference"],
        default="all",
        help="Which demo to run"
    )
    
    args = parser.parse_args()
    
    print("🌟 PolarFree Flow Matching Demo")
    print("="*60)
    print("This demo showcases the new flow matching capabilities")
    print("integrated into the PolarFree framework.")
    print("="*60)
    print()
    
    # Run selected demos
    if args.demo in ["all", "basics"]:
        demo_flow_matching_basics()
    
    if args.demo in ["all", "pretrained"]:
        demo_pretrained_loading()
    
    if args.demo in ["all", "training"]:
        demo_training_simulation()
    
    if args.demo in ["all", "inference"]:
        demo_inference_comparison()
    
    print("🎉 All demos completed successfully!")
    print("\nNext steps:")
    print("1. Train your model: python train_flow_matching.py")
    print("2. Test the model: python test_flow_matching.py")
    print("3. Read the documentation: README_FlowMatching.md")


if __name__ == "__main__":
    main()
